# AstrBot 多环境配置指南

AstrBot 现在支持通过环境变量进行多环境配置，让您可以轻松地在本机、开发、预览和生产环境之间切换。

## 🚀 快速开始

### 1. 创建环境配置文件

复制示例配置文件：

```bash
# 本机环境
cp config/.env.example config/.env.local

# 开发环境
cp config/.env.example config/.env.dev

# 线上预览环境
cp config/.env.example config/.env.beta

# 正式环境
cp config/.env.example config/.env.prod
```

### 2. 编辑配置文件

根据您的需求编辑对应的配置文件，例如 `config/.env.prod`：

```bash
# 正式环境配置
APP_ENV=prod
DEBUG=false

# PostgreSQL 数据库
DATABASE_TYPE=postgresql
DATABASE_URL=postgresql://username:password@localhost:5432/astrbot_prod

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/astrbot_prod.log

# Web 服务器配置
WEB_HOST=0.0.0.0
WEB_PORT=6185

# 功能开关
ENABLE_METRICS=true
ENABLE_WEB_CONSOLE=false
```

### 3. 启动应用

```bash
# 使用启动脚本（推荐）
./scripts/start.sh -e prod

# 或直接设置环境变量
export APP_ENV=prod
python main.py
```

## 📋 配置项说明

### 基础配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `APP_ENV` | 应用环境 | `local` | `prod` |
| `DEBUG` | 调试模式 | `false` | `true` |

### 数据库配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `DATABASE_TYPE` | 数据库类型 | `postgresql` | `postgresql` |
| `DATABASE_URL` | 数据库连接URL | `*******************************************************` | `********************************/db` |

#### 数据库URL格式

**SQLite**：
```
sqlite:///path/to/database.db    # 文件数据库
sqlite:///:memory:               # 内存数据库
```

**PostgreSQL**：
```
postgresql://username:password@hostname:port/database_name
```

### Web服务器配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `WEB_HOST` | 服务器主机 | `127.0.0.1` | `0.0.0.0` |
| `WEB_PORT` | 服务器端口 | `6185` | `8080` |

### 日志配置

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `LOG_LEVEL` | 日志级别 | `INFO` | `DEBUG` |
| `LOG_FILE` | 日志文件路径 | `logs/astrbot.log` | `logs/prod.log` |

### 功能开关

| 配置项 | 说明 | 默认值 | 示例 |
|--------|------|--------|------|
| `ENABLE_METRICS` | 启用指标收集 | `true` | `false` |
| `ENABLE_WEB_CONSOLE` | 启用Web控制台 | `true` | `false` |

## 🔄 数据库迁移

### 从 SQLite 迁移到 PostgreSQL

1. **准备 PostgreSQL 数据库**：
   ```sql
   CREATE DATABASE astrbot_prod;
   CREATE USER astrbot_user WITH PASSWORD 'your_password';
   GRANT ALL PRIVILEGES ON DATABASE astrbot_prod TO astrbot_user;
   ```

2. **配置环境文件**：
   ```bash
   # config/.env.production
   DATABASE_TYPE=postgresql
   DATABASE_URL=postgresql://astrbot_user:your_password@localhost:5432/astrbot_prod
   ```

3. **执行迁移**：
   ```bash
   # 检查连接
   ./scripts/migrate_database.py --dry-run
   
   # 执行迁移
   ./scripts/migrate_database.py
   
   # 或使用启动脚本
   ./scripts/start.sh --migrate
   ```

## 🛠️ 启动脚本使用

### Linux/macOS

```bash
# 基本用法
./scripts/start.sh                    # 使用默认环境 (development)
./scripts/start.sh -e production      # 使用生产环境
./scripts/start.sh -e testing         # 使用测试环境

# 其他功能
./scripts/start.sh --check-config     # 检查配置文件
./scripts/start.sh --migrate          # 执行数据库迁移
./scripts/start.sh --help             # 显示帮助信息
```

### Windows

```batch
REM 基本用法
scripts\start.bat                     REM 使用默认环境
scripts\start.bat -e production       REM 使用生产环境

REM 其他功能
scripts\start.bat --check-config      REM 检查配置文件
scripts\start.bat --migrate           REM 执行数据库迁移
scripts\start.bat --help              REM 显示帮助信息
```

## 🔧 环境变量优先级

配置的加载优先级（从高到低）：

1. 系统环境变量
2. `.env.{APP_ENV}` 文件
3. `.env` 文件（如果存在）
4. 代码中的默认值

## 📝 最佳实践

### 开发环境
- 使用 SQLite 数据库以简化开发
- 启用调试模式和详细日志
- 启用Web控制台便于调试

### 生产环境
- 使用 PostgreSQL 数据库获得更好性能
- 关闭调试模式
- 设置适当的日志级别
- 考虑关闭Web控制台以提高安全性

### 测试环境
- 使用内存数据库 (`sqlite:///:memory:`) 加快测试速度
- 启用详细日志便于调试测试问题

## ❓ 常见问题

**Q: 如何知道当前使用的是哪个环境？**
A: 查看启动日志，会显示当前加载的环境配置文件。

**Q: 可以在运行时切换环境吗？**
A: 不可以，需要重启应用才能切换环境。

**Q: 数据库迁移会覆盖现有数据吗？**
A: 不会，迁移脚本会将数据从源数据库复制到目标数据库，不会删除源数据。

**Q: 如何回滚到 SQLite？**
A: 修改环境配置文件中的 `DATABASE_TYPE` 和 `DATABASE_URL`，然后重启应用。
