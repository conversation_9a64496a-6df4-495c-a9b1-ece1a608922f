# 正式环境配置
APP_ENV=prod
DEBUG=false

# 数据库配置 - PostgreSQL
DATABASE_TYPE=postgresql
DATABASE_URL=postgresql://username:password@localhost:5432/ai_chat_astrbot

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/astrbot_prod.log

# Web 配置
WEB_HOST=0.0.0.0
WEB_PORT=6185

# 其他配置
ENABLE_METRICS=true
ENABLE_WEB_CONSOLE=false


OIDC_CLIENT_ID=a96b9fc719c64cb283d362db7928b5ef
OIDC_CLIENT_SECRET=0zA9sVj9ktv9mTWcg9k9BG44cBwyqeIH
OIDC_TOKEN_URL=http://sso.dev.20382038.xyz/api/v1/oauth/token
OIDC_USERINFO_URL=http://sso.dev.20382038.xyz/api/v1/oauth/userinfo
OIDC_ISSUER=http://sso.dev.20382038.xyz
OIDC_AUTHORIZATION_URL=http://sso.dev.20382038.xyz/oauth/authorize
OIDC_REDIRECT_URL=http://localhost:5173/chat/callback
OIDC_LOGOUT_URL=http://sso.dev.20382038.xyz/oauth/logout
OIDC_INTROSPECTION_URL=http://sso.dev.20382038.xyz/api/v1/oauth/introspect
OIDC_JWKS_URL=http://sso.dev.20382038.xyz/api/v1/oauth/.well-known/jwks.json


# 权限系统配置
PERMISSION_ROOT=ryjx
PERMISSION_APP=chat
PERMISSION_ALL=*

# IAM中心接口配置
IAM_API_BASE_URL=http://sso.dev.20382038.xyz
IAM_API_HOST_URL=http://sso.dev.20382038.xyz
IAM_API_INTERNAL_HOST=http://sso.dev.20382038.xyz
IAM_API_OUTER_HOST=http://sso.dev.20382038.xyz
SERVICE_DISCOVERY_URL=

# RAG 插件配置
RAG_API_BASE_URL=https://dev-rag-nova.jx.ruyi.cn
RAG_API_KEY=pk-7MG5s5oSihnbVfs3AJjmrA:sk-Mi6mY84owF4Hn7HR6yFKS6
RAG_DEFAULT_USERKEY=nova_chat_bot_dev
RAG_DEFAULT_USERNAME=Nova聊天机器人Dev
RAG_DEFAULT_MODEL_TYPE=ccp
RAG_REQUEST_TIMEOUT=30
RAG_MAX_RETRIES=3
RAG_DEBUG=false
# 默认工作流 repo id
DEFAULT_RAG_REPO_ID=1018
# 通用消息发送工作流ID
IM_SEND_MSG_WORKFLOW_ID="im:send_msg"

# Redis配置
REDIS_URI=*********:6379
REDIS_DB=0
REDIS_PASSWORD=e2xd5f@Gm3
CORS_CACHE_TTL=2