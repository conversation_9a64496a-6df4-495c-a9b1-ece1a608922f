# AstrBot 环境配置示例文件
# 复制此文件为 .env.local, .env.dev, .env.beta 或 .env.prod 并修改相应配置

# 应用环境 (local, dev, beta, prod)
APP_ENV=local

# 调试模式
DEBUG=true

# 数据库配置 - 所有环境都使用 PostgreSQL
DATABASE_TYPE=postgresql
DATABASE_URL=postgresql://username:password@localhost:5432/astrbot

# 开发环境数据库示例
# DATABASE_URL=*********************************************/staff_sso

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=logs/astrbot.log

# Web 服务器配置
WEB_HOST=127.0.0.1
WEB_PORT=6185

# 功能开关
ENABLE_METRICS=true
ENABLE_WEB_CONSOLE=true

# 其他配置
# HTTP_PROXY=http://127.0.0.1:7890
# HTTPS_PROXY=http://127.0.0.1:7890
