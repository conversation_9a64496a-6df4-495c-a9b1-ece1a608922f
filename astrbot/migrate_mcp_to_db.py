#!/usr/bin/env python3
"""
MCP 服务器配置迁移脚本
将 JSON 文件中的 MCP 服务器配置迁移到 PostgreSQL 数据库
"""

import json
import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from astrbot.core import logger
from astrbot.core.db.orm import SessionLocal, init_db
from astrbot.core.db.models import MCPServer
from astrbot.core.utils.astrbot_path import get_astrbot_data_path


def get_mcp_config_path():
    """获取 MCP 配置文件路径"""
    data_dir = get_astrbot_data_path()
    return os.path.join(data_dir, "mcp_server.json")


def load_json_config():
    """从 JSON 文件加载 MCP 配置"""
    config_path = get_mcp_config_path()
    
    if not os.path.exists(config_path):
        logger.info(f"JSON 配置文件不存在: {config_path}")
        return None
    
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
        logger.info(f"成功加载 JSON 配置文件: {config_path}")
        return config
    except Exception as e:
        logger.error(f"加载 JSON 配置文件失败: {e}")
        return None


def migrate_to_database(config):
    """将配置迁移到数据库"""
    if not config or "mcpServers" not in config:
        logger.info("没有找到需要迁移的 MCP 服务器配置")
        return 0
    
    # 初始化数据库
    init_db()
    
    migrated_count = 0
    skipped_count = 0
    
    try:
        with SessionLocal() as session:
            for name, server_config in config["mcpServers"].items():
                # 检查服务器是否已存在
                existing_server = session.query(MCPServer).filter(MCPServer.name == name).first()
                
                if existing_server:
                    logger.info(f"服务器 {name} 已存在，跳过迁移")
                    skipped_count += 1
                    continue
                
                # 分离 active 字段和其他配置
                active = server_config.pop("active", True)
                
                # 创建新的服务器记录
                new_server = MCPServer(
                    name=name,
                    active=active,
                    config=server_config
                )
                
                session.add(new_server)
                logger.info(f"迁移服务器配置: {name} (active: {active})")
                migrated_count += 1
            
            # 提交所有更改
            session.commit()
            logger.info(f"成功迁移 {migrated_count} 个服务器配置到数据库")
            
    except Exception as e:
        logger.error(f"迁移到数据库失败: {e}")
        return -1
    
    return migrated_count


def backup_json_config():
    """备份原始 JSON 配置文件"""
    config_path = get_mcp_config_path()
    
    if not os.path.exists(config_path):
        return True
    
    backup_path = f"{config_path}.backup"
    
    try:
        import shutil
        shutil.copy2(config_path, backup_path)
        logger.info(f"已备份原始配置文件到: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"备份配置文件失败: {e}")
        return False


def verify_migration():
    """验证迁移结果"""
    try:
        init_db()
        
        with SessionLocal() as session:
            servers = session.query(MCPServer).all()
            
            logger.info(f"数据库中共有 {len(servers)} 个 MCP 服务器配置:")
            for server in servers:
                logger.info(f"  - {server.name} (active: {server.active})")
                logger.debug(f"    配置: {server.config}")
        
        return True
    except Exception as e:
        logger.error(f"验证迁移结果失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 MCP 服务器配置迁移工具")
    print("=" * 50)
    
    # 1. 加载 JSON 配置
    print("\n📂 加载 JSON 配置文件...")
    config = load_json_config()
    
    if not config:
        print("❌ 没有找到有效的 JSON 配置文件")
        return 1
    
    server_count = len(config.get("mcpServers", {}))
    print(f"✅ 找到 {server_count} 个 MCP 服务器配置")
    
    if server_count == 0:
        print("ℹ️  没有需要迁移的服务器配置")
        return 0
    
    # 2. 备份原始配置
    print("\n💾 备份原始配置文件...")
    if backup_json_config():
        print("✅ 配置文件备份成功")
    else:
        print("⚠️  配置文件备份失败，但继续迁移")
    
    # 3. 迁移到数据库
    print("\n🔄 迁移配置到数据库...")
    migrated_count = migrate_to_database(config)
    
    if migrated_count < 0:
        print("❌ 迁移失败")
        return 1
    elif migrated_count == 0:
        print("ℹ️  所有配置都已存在，无需迁移")
    else:
        print(f"✅ 成功迁移 {migrated_count} 个服务器配置")
    
    # 4. 验证迁移结果
    print("\n🔍 验证迁移结果...")
    if verify_migration():
        print("✅ 迁移验证成功")
    else:
        print("❌ 迁移验证失败")
        return 1
    
    print("\n" + "=" * 50)
    print("🎉 MCP 服务器配置迁移完成！")
    print("\n💡 说明:")
    print("- 原始 JSON 配置文件已备份为 .backup 文件")
    print("- 所有 MCP 服务器配置现在存储在 PostgreSQL 数据库中")
    print("- 可以安全删除原始 JSON 配置文件")
    print("- 系统将自动使用数据库中的配置")
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  迁移被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 迁移过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
