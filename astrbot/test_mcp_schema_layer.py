#!/usr/bin/env python3
"""
MCP Schema 层测试脚本
测试 Schema 验证、Service 层操作和数据库集成
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from astrbot.core import logger
from astrbot.core.db.orm import SessionLocal, init_db
from astrbot.core.db.services import MCPServerService, MCPMarketService
from astrbot.core.db.schemas import (
    MCPServerCreate, MCPServerUpdate, MCPServerAddRequest, MCPServerUpdateRequest,
    MCPMarketCreate, MCPMarketUpdate
)
from pydantic import ValidationError


def test_mcp_server_schemas():
    """测试 MCP Server Schema 验证"""
    print("🧪 测试 MCP Server Schema 验证...")
    
    # 测试有效的服务器创建请求
    try:
        valid_request = MCPServerAddRequest(
            name="test_server",
            active=True,
            command="python",
            args=["-m", "test_mcp"],
            cwd="/path/to/server"
        )
        print(f"  ✅ 有效请求验证通过: {valid_request.name}")
        
        # 测试配置转换
        config = valid_request.to_server_config()
        print(f"  ✅ 配置转换成功: {config}")
        
    except ValidationError as e:
        print(f"  ❌ 有效请求验证失败: {e}")
        return False
    
    # 测试无效的服务器创建请求
    try:
        invalid_request = MCPServerAddRequest(
            name="",  # 空名称应该失败
            active=True
        )
        print("  ❌ 无效请求应该失败但通过了")
        return False
    except ValidationError:
        print("  ✅ 无效请求正确被拒绝")
    
    # 测试嵌套配置格式
    try:
        nested_request = MCPServerAddRequest(
            name="nested_server",
            mcpServers={
                "nested_server": {
                    "command": "node",
                    "args": ["server.js"],
                    "active": True
                }
            }
        )
        config = nested_request.to_server_config()
        print(f"  ✅ 嵌套配置转换成功: {config}")
        
    except ValidationError as e:
        print(f"  ❌ 嵌套配置验证失败: {e}")
        return False
    
    print("✅ MCP Server Schema 测试通过")
    return True


def test_mcp_market_schemas():
    """测试 MCP Market Schema 验证"""
    print("\n🧪 测试 MCP Market Schema 验证...")
    
    # 测试有效的市场条目创建
    try:
        valid_market = MCPMarketCreate(
            name="test_market_item",
            description="测试市场条目",
            author="test_author",
            repo="https://github.com/test/repo",
            version="1.0.0",
            tags=["test", "mcp"],
            stars=100,
            pinned=False
        )
        print(f"  ✅ 有效市场条目验证通过: {valid_market.name}")
        
    except ValidationError as e:
        print(f"  ❌ 有效市场条目验证失败: {e}")
        return False
    
    # 测试无效的市场条目（负数星标）
    try:
        invalid_market = MCPMarketCreate(
            name="invalid_market",
            stars=-10  # 负数应该失败
        )
        print("  ❌ 无效市场条目应该失败但通过了")
        return False
    except ValidationError:
        print("  ✅ 无效市场条目正确被拒绝")
    
    print("✅ MCP Market Schema 测试通过")
    return True


def test_mcp_server_service():
    """测试 MCP Server Service 层"""
    print("\n🧪 测试 MCP Server Service 层...")
    
    try:
        init_db()
        
        with SessionLocal() as session:
            # 清理测试数据
            MCPServerService.delete_server(session, "test_schema_server")
            
            # 测试创建服务器
            server_create = MCPServerCreate(
                name="test_schema_server",
                active=True,
                config={
                    "command": "python",
                    "args": ["-m", "test_server"],
                    "cwd": "/test/path"
                }
            )
            
            created_server = MCPServerService.create_server(session, server_create)
            print(f"  ✅ 创建服务器成功: {created_server.name}")
            
            # 测试获取服务器
            retrieved_server = MCPServerService.get_server(session, "test_schema_server")
            if retrieved_server and retrieved_server.name == "test_schema_server":
                print(f"  ✅ 获取服务器成功: {retrieved_server.name}")
            else:
                print("  ❌ 获取服务器失败")
                return False
            
            # 测试更新服务器
            server_update = MCPServerUpdate(
                active=False,
                config={
                    "command": "node",
                    "args": ["server.js"],
                    "updated": True
                }
            )
            
            updated_server = MCPServerService.update_server(session, "test_schema_server", server_update)
            if updated_server and not updated_server.active:
                print(f"  ✅ 更新服务器成功: active={updated_server.active}")
            else:
                print("  ❌ 更新服务器失败")
                return False
            
            # 测试删除服务器
            success = MCPServerService.delete_server(session, "test_schema_server")
            if success:
                print("  ✅ 删除服务器成功")
            else:
                print("  ❌ 删除服务器失败")
                return False
        
        print("✅ MCP Server Service 测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ Service 测试失败: {e}")
        return False


def test_mcp_market_service():
    """测试 MCP Market Service 层"""
    print("\n🧪 测试 MCP Market Service 层...")
    
    try:
        init_db()
        
        with SessionLocal() as session:
            # 清理测试数据
            MCPMarketService.delete_market_item(session, "test_schema_market")
            
            # 测试创建市场条目
            market_create = MCPMarketCreate(
                name="test_schema_market",
                description="测试 Schema 市场条目",
                author="schema_tester",
                repo="https://github.com/test/schema",
                version="1.0.0",
                tags=["schema", "test"],
                stars=50,
                pinned=True
            )
            
            created_item = MCPMarketService.create_market_item(session, market_create)
            print(f"  ✅ 创建市场条目成功: {created_item.name}")
            
            # 测试获取市场条目
            retrieved_item = MCPMarketService.get_market_item(session, "test_schema_market")
            if retrieved_item and retrieved_item.pinned:
                print(f"  ✅ 获取市场条目成功: pinned={retrieved_item.pinned}")
            else:
                print("  ❌ 获取市场条目失败")
                return False
            
            # 测试批量创建
            batch_items = [
                MCPMarketCreate(
                    name=f"batch_item_{i}",
                    description=f"批量测试条目 {i}",
                    author="batch_tester",
                    stars=i * 10
                )
                for i in range(1, 4)
            ]
            
            created_count = MCPMarketService.bulk_create_market_items(session, batch_items)
            print(f"  ✅ 批量创建成功: {created_count} 个条目")
            
            # 测试搜索
            search_results = MCPMarketService.search_market_items(session, "batch")
            if len(search_results) >= 3:
                print(f"  ✅ 搜索功能正常: 找到 {len(search_results)} 个结果")
            else:
                print(f"  ⚠️  搜索结果不完整: 找到 {len(search_results)} 个结果")
            
            # 清理测试数据
            for i in range(1, 4):
                MCPMarketService.delete_market_item(session, f"batch_item_{i}")
            MCPMarketService.delete_market_item(session, "test_schema_market")
        
        print("✅ MCP Market Service 测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ Market Service 测试失败: {e}")
        return False


def test_integration():
    """测试 Schema + Service 集成"""
    print("\n🧪 测试 Schema + Service 集成...")
    
    try:
        # 模拟 API 请求数据
        api_request_data = {
            "name": "integration_test_server",
            "active": True,
            "command": "python",
            "args": ["-m", "integration_test"],
            "cwd": "/integration/test",
            "env": {"TEST_MODE": "true"}
        }
        
        # 使用 Schema 验证和转换
        request_schema = MCPServerAddRequest(**api_request_data)
        server_config = request_schema.to_server_config()
        active = server_config.pop("active", True)
        
        # 使用 Service 层操作
        init_db()
        with SessionLocal() as session:
            # 清理
            MCPServerService.delete_server(session, "integration_test_server")
            
            # 创建
            server_create = MCPServerCreate(
                name=request_schema.name,
                active=active,
                config=server_config
            )
            
            created_server = MCPServerService.create_server(session, server_create)
            print(f"  ✅ 集成测试创建成功: {created_server.name}")
            
            # 验证数据完整性
            if (created_server.config.get("command") == "python" and 
                created_server.config.get("env", {}).get("TEST_MODE") == "true"):
                print("  ✅ 数据完整性验证通过")
            else:
                print("  ❌ 数据完整性验证失败")
                return False
            
            # 清理
            MCPServerService.delete_server(session, "integration_test_server")
        
        print("✅ Schema + Service 集成测试通过")
        return True
        
    except Exception as e:
        print(f"  ❌ 集成测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 MCP Schema 层测试")
    print("=" * 50)
    
    tests = [
        ("MCP Server Schema", test_mcp_server_schemas),
        ("MCP Market Schema", test_mcp_market_schemas),
        ("MCP Server Service", test_mcp_server_service),
        ("MCP Market Service", test_mcp_market_service),
        ("Schema + Service 集成", test_integration),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Schema 层实现正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查 Schema 层实现")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
