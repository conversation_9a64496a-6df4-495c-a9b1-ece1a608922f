# Pydantic Settings 迁移指南

## 概述

`env_config.py` 已从简单的环境变量读取器升级为基于 Pydantic Settings 的强类型配置管理系统。这提供了更好的类型安全、数据验证和配置管理能力。

## 主要改进

### ✅ 新增功能

1. **强类型支持**
   - 所有配置项都有明确的类型定义
   - 自动类型转换和验证
   - IDE 智能提示和类型检查

2. **数据验证**
   - 端口号范围验证 (1-65535)
   - 日志级别有效性验证
   - 环境类型限制 (local/dev/beta/prod)

3. **完整的 OAuth/IAM 配置**
   - 内置 OAuth2 配置字段
   - RYJX IAM 集成配置
   - 自动环境变量映射

4. **向后兼容**
   - 保留原有的 `get()`, `get_bool()`, `get_int()` 方法
   - 保持相同的环境判断属性
   - 无需修改现有代码

### 🔧 配置字段

#### 基础配置
```python
APP_ENV: Literal['local', 'dev', 'beta', 'prod']  # 应用环境
DATABASE_TYPE: str                                # 数据库类型
DATABASE_URL: str                                 # 数据库连接URL
WEB_HOST: str                                     # Web服务器主机
WEB_PORT: int                                     # Web服务器端口
LOG_LEVEL: str                                    # 日志级别
LOG_FILE: str                                     # 日志文件路径
DEBUG: bool                                       # 调试模式
ENABLE_METRICS: bool                              # 启用指标收集
ENABLE_WEB_CONSOLE: bool                          # 启用Web控制台
```

#### OAuth2 配置
```python
OIDC_CLIENT_ID: Optional[str]                     # OAuth2 客户端 ID
OIDC_CLIENT_SECRET: Optional[str]                 # OAuth2 客户端密钥
OIDC_AUTHORIZATION_URL: Optional[str]             # OAuth2 授权 URL
OIDC_TOKEN_URL: Optional[str]                     # OAuth2 令牌 URL
OIDC_USERINFO_URL: Optional[str]                  # OAuth2 用户信息 URL
OIDC_REDIRECT_URL: Optional[str]                  # OAuth2 回调 URL
```

#### RYJX IAM 配置
```python
IAM_API_BASE_URL: Optional[str]                   # IAM API 基础 URL
SERVICE_DISCOVERY_URL: Optional[str]              # 服务发现 URL
```

## 使用方法

### 基本使用
```python
from astrbot.core.config.env_config import env_config

# 直接访问配置属性（推荐）
print(f"Web 端口: {env_config.WEB_PORT}")
print(f"数据库 URL: {env_config.DATABASE_URL}")
print(f"是否调试模式: {env_config.DEBUG}")

# 环境判断
if env_config.is_prod:
    print("生产环境")
elif env_config.is_dev:
    print("开发环境")
```

### 兼容性使用
```python
# 旧的方法仍然可用
host = env_config.get('WEB_HOST', 'localhost')
port = env_config.get_int('WEB_PORT', 8080)
debug = env_config.get_bool('DEBUG', False)
```

### OAuth 配置访问
```python
# 检查 OAuth 配置
if env_config.OIDC_CLIENT_ID:
    print(f"OAuth 客户端 ID: {env_config.OIDC_CLIENT_ID}")
    print(f"授权 URL: {env_config.OIDC_AUTHORIZATION_URL}")
```

## 环境变量映射

| 配置属性 | 环境变量 | 默认值 | 说明 |
|---------|----------|--------|------|
| `APP_ENV` | `APP_ENV` | `local` | 应用环境 |
| `DATABASE_TYPE` | `DATABASE_TYPE` | `sqlite` | 数据库类型 |
| `DATABASE_URL` | `DATABASE_URL` | `postgresql://...` | 数据库连接 |
| `WEB_HOST` | `WEB_HOST` | `127.0.0.1` | Web 主机 |
| `WEB_PORT` | `WEB_PORT` | `6185` | Web 端口 |
| `LOG_LEVEL` | `LOG_LEVEL` | `INFO` | 日志级别 |
| `DEBUG` | `DEBUG` | `False` | 调试模式 |
| `OIDC_CLIENT_ID` | `OIDC_CLIENT_ID` | `None` | OAuth 客户端 ID |
| `OIDC_CLIENT_SECRET` | `OIDC_CLIENT_SECRET` | `None` | OAuth 客户端密钥 |
| `IAM_API_BASE_URL` | `IAM_API_BASE_URL` | `None` | IAM API URL |

## 数据验证

### 端口号验证
```python
# 有效端口号 (1-65535)
WEB_PORT=6185  # ✅ 有效

# 无效端口号
WEB_PORT=99999  # ❌ 抛出 ValueError
```

### 日志级别验证
```python
# 有效日志级别
LOG_LEVEL=INFO     # ✅ 有效
LOG_LEVEL=DEBUG    # ✅ 有效

# 无效日志级别
LOG_LEVEL=INVALID  # ❌ 抛出 ValueError
```

### 环境类型验证
```python
# 有效环境类型
APP_ENV=local  # ✅ 有效
APP_ENV=prod   # ✅ 有效

# 无效环境类型
APP_ENV=invalid  # ❌ 抛出 ValueError
```

## 测试和验证

### 运行测试脚本
```bash
cd astrbot
python test_env_config.py
```

测试脚本会验证：
- ✅ 基础配置加载
- ✅ OAuth 配置读取
- ✅ IAM 配置读取
- ✅ 兼容性方法
- ✅ 数据验证
- ✅ 环境文件加载

### 手动验证
```python
from astrbot.core.config.env_config import env_config

# 检查配置是否正确加载
print(f"当前环境: {env_config.APP_ENV}")
print(f"Web 配置: {env_config.WEB_HOST}:{env_config.WEB_PORT}")
print(f"OAuth 配置: {bool(env_config.OIDC_CLIENT_ID)}")
```

## 迁移注意事项

### 1. 依赖要求
确保安装了 Pydantic：
```bash
pip install pydantic
# 或者对于 Pydantic v2
pip install pydantic-settings
```

### 2. 环境变量格式
- 布尔值：`true`/`false`, `1`/`0`, `yes`/`no`, `on`/`off`
- 整数：直接数字字符串
- 字符串：直接字符串值

### 3. 配置文件
环境配置文件 (`.env.{environment}`) 格式保持不变：
```bash
# .env.local
APP_ENV=local
WEB_HOST=127.0.0.1
WEB_PORT=6185
DEBUG=true
OIDC_CLIENT_ID=your-client-id
```

### 4. 错误处理
新系统会在配置无效时抛出 `ValueError`，建议在应用启动时捕获：
```python
try:
    from astrbot.core.config.env_config import env_config
    print(f"配置加载成功，环境: {env_config.app_env}")
except ValueError as e:
    print(f"配置验证失败: {e}")
    sys.exit(1)
```

## 故障排除

### 常见问题

1. **ImportError: No module named 'pydantic_settings'**
   ```bash
   pip install pydantic-settings
   ```

2. **ValueError: 端口号必须在 1-65535 之间**
   - 检查 `WEB_PORT` 环境变量值
   - 确保端口号在有效范围内

3. **ValueError: 日志级别必须是以下之一**
   - 检查 `LOG_LEVEL` 环境变量
   - 使用有效值：DEBUG, INFO, WARNING, ERROR, CRITICAL

4. **配置文件未加载**
   - 检查 `APP_ENV` 环境变量
   - 确认 `config/.env.{environment}` 文件存在

## 总结

Pydantic Settings 升级提供了：
- ✅ 强类型配置管理
- ✅ 自动数据验证
- ✅ 更好的开发体验
- ✅ 完整的向后兼容性
- ✅ OAuth/IAM 配置集成

升级后的配置系统更加健壮、类型安全，同时保持了与现有代码的完全兼容性。
