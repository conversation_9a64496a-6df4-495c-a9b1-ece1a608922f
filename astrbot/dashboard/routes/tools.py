import traceback

import aiohttp
from quart import request
from pydantic import ValidationError

from astrbot.core import logger
from astrbot.core.core_lifecycle import AstrBotCoreLifecycle
from astrbot.core.db.orm import SessionLocal, init_db
from astrbot.core.db.services import MCPServerService, MCPMarketService
from astrbot.core.db.schemas import (
    MCPServerCreate, MCPServerUpdate, MCPServerAddRequest, MCPServerUpdateRequest,
    MCPServerDeleteRequest, MCPServerTestRequest, MCPMarketCreate, MCPMarketInitRequest
)

from .route import Response, Route, RouteContext

DEFAULT_MCP_CONFIG = {"mcpServers": {}}


class ToolsRoute(Route):
    def __init__(
        self, context: RouteContext, core_lifecycle: AstrBotCoreLifecycle
    ) -> None:
        super().__init__(context)
        self.core_lifecycle = core_lifecycle
        self.routes = {
            "/tools/mcp/servers": ("GET", self.get_mcp_servers),
            "/tools/mcp/add": ("POST", self.add_mcp_server),
            "/tools/mcp/update": ("POST", self.update_mcp_server),
            "/tools/mcp/delete": ("POST", self.delete_mcp_server),
            "/tools/mcp/market": ("GET", self.get_mcp_markets),
            "/tools/mcp/market/init": ("POST", self.init_mcp_market_from_remote),
            "/tools/mcp/test": ("POST", self.test_mcp_connection),
        }
        self.register_routes()
        self.tool_mgr = self.core_lifecycle.provider_manager.llm_tools

    def load_mcp_config(self):
        """从数据库加载 MCP 配置（兼容性方法）"""
        try:
            init_db()

            with SessionLocal() as session:
                # 使用 Service 层获取服务器列表
                server_responses = MCPServerService.get_all_servers(session)
                config = {"mcpServers": {}}

                for server_response in server_responses:
                    config["mcpServers"][server_response.name] = {
                        "active": server_response.active,
                        **server_response.config  # 展开配置字典
                    }

                return config
        except Exception as e:
            logger.error(f"从数据库加载 MCP 配置失败: {e}")
            return DEFAULT_MCP_CONFIG

    async def get_mcp_servers(self):
        try:
            init_db()

            with SessionLocal() as session:
                # 使用 Service 层获取服务器列表
                server_responses = MCPServerService.get_all_servers(session)

                servers = []
                for server_response in server_responses:
                    # 转换为字典格式
                    server_info = server_response.dict()

                    # 如果MCP客户端已初始化，从客户端获取工具名称
                    for (
                        name_key,
                        mcp_client,
                    ) in self.tool_mgr.mcp_client_dict.items():
                        if name_key == server_response.name:
                            server_info["tools"] = [tool.name for tool in mcp_client.tools]
                            server_info["errlogs"] = mcp_client.server_errlogs
                            break
                    else:
                        server_info["tools"] = []
                        server_info["errlogs"] = []

                    servers.append(server_info)

            return Response().ok(servers).__dict__
        except Exception as e:
            logger.error(traceback.format_exc())
            return Response().error(f"获取 MCP 服务器列表失败: {str(e)}").__dict__

    async def add_mcp_server(self):
        try:
            server_data = await request.json

            # 使用 Schema 验证请求数据
            try:
                request_schema = MCPServerAddRequest(**server_data)
            except ValidationError as e:
                return Response().error(f"请求数据验证失败: {e}").__dict__

            # 转换为服务器配置
            server_config = request_schema.to_server_config()
            active = server_config.pop("active", True)

            # 检查配置是否有效
            if not server_config:
                return Response().error("必须提供有效的服务器配置").__dict__

            init_db()
            with SessionLocal() as session:
                try:
                    # 使用 Service 层创建服务器
                    server_create = MCPServerCreate(
                        name=request_schema.name,
                        active=active,
                        config=server_config
                    )

                    server_response = MCPServerService.create_server(session, server_create)

                    # 启用 MCP 服务器
                    try:
                        await self.tool_mgr.enable_mcp_server(
                            request_schema.name, server_config, timeout=30
                        )
                    except TimeoutError:
                        return Response().error(f"启用 MCP 服务器 {request_schema.name} 超时。").__dict__
                    except Exception as e:
                        logger.error(traceback.format_exc())
                        return (
                            Response()
                            .error(f"启用 MCP 服务器 {request_schema.name} 失败: {str(e)}")
                            .__dict__
                        )

                    return Response().ok(None, f"成功添加 MCP 服务器 {request_schema.name}").__dict__

                except ValueError as e:
                    return Response().error(str(e)).__dict__

        except Exception as e:
            logger.error(traceback.format_exc())
            return Response().error(f"添加 MCP 服务器失败: {str(e)}").__dict__

    async def update_mcp_server(self):
        try:
            server_data = await request.json

            # 使用 Schema 验证请求数据
            try:
                request_schema = MCPServerUpdateRequest(**server_data)
            except ValidationError as e:
                return Response().error(f"请求数据验证失败: {e}").__dict__

            init_db()
            with SessionLocal() as session:
                try:
                    # 检查服务器是否存在
                    existing_server = MCPServerService.get_server(session, request_schema.name)
                    if not existing_server:
                        return Response().error(f"服务器 {request_schema.name} 不存在").__dict__

                    # 转换为服务器配置
                    server_config = request_schema.to_server_config()
                    active = server_config.pop("active", existing_server.active)

                    # 仅更新活动状态的特殊处理
                    only_update_active = len(server_config) == 0

                    # 如果只更新活动状态，保留原始配置
                    if only_update_active:
                        server_config = existing_server.config.copy()

                    # 使用 Service 层更新服务器
                    server_update = MCPServerUpdate(
                        active=active,
                        config=server_config
                    )

                    updated_server = MCPServerService.update_server(session, request_schema.name, server_update)
                    if not updated_server:
                        return Response().error(f"更新服务器 {request_schema.name} 失败").__dict__

                    # 处理MCP客户端状态变化
                    if active:
                        if request_schema.name in self.tool_mgr.mcp_client_dict or not only_update_active:
                            try:
                                await self.tool_mgr.disable_mcp_server(request_schema.name, timeout=10)
                            except TimeoutError as e:
                                return (
                                    Response()
                                    .error(f"启用前停用 MCP 服务器时 {request_schema.name} 超时: {str(e)}")
                                    .__dict__
                                )
                            except Exception as e:
                                logger.error(traceback.format_exc())
                                return (
                                    Response()
                                    .error(f"启用前停用 MCP 服务器时 {request_schema.name} 失败: {str(e)}")
                                    .__dict__
                                )
                        try:
                            await self.tool_mgr.enable_mcp_server(
                                request_schema.name, server_config, timeout=30
                            )
                        except TimeoutError:
                            return (
                                Response().error(f"启用 MCP 服务器 {request_schema.name} 超时。").__dict__
                            )
                        except Exception as e:
                            logger.error(traceback.format_exc())
                            return (
                                Response()
                                .error(f"启用 MCP 服务器 {request_schema.name} 失败: {str(e)}")
                                .__dict__
                            )
                    else:
                        # 如果要停用服务器
                        if request_schema.name in self.tool_mgr.mcp_client_dict:
                            try:
                                await self.tool_mgr.disable_mcp_server(request_schema.name, timeout=10)
                            except TimeoutError:
                                return (
                                    Response()
                                    .error(f"停用 MCP 服务器 {request_schema.name} 超时。")
                                    .__dict__
                                )
                            except Exception as e:
                                logger.error(traceback.format_exc())
                                return (
                                    Response()
                                    .error(f"停用 MCP 服务器 {request_schema.name} 失败: {str(e)}")
                                    .__dict__
                                )

                    return Response().ok(None, f"成功更新 MCP 服务器 {request_schema.name}").__dict__

                except ValueError as e:
                    return Response().error(str(e)).__dict__

        except Exception as e:
            logger.error(traceback.format_exc())
            return Response().error(f"更新 MCP 服务器失败: {str(e)}").__dict__

    async def delete_mcp_server(self):
        try:
            server_data = await request.json

            # 使用 Schema 验证请求数据
            try:
                request_schema = MCPServerDeleteRequest(**server_data)
            except ValidationError as e:
                return Response().error(f"请求数据验证失败: {e}").__dict__

            init_db()
            with SessionLocal() as session:
                try:
                    # 使用 Service 层删除服务器
                    success = MCPServerService.delete_server(session, request_schema.name)

                    if success:
                        # 停用 MCP 客户端
                        if request_schema.name in self.tool_mgr.mcp_client_dict:
                            try:
                                await self.tool_mgr.disable_mcp_server(request_schema.name, timeout=10)
                            except TimeoutError:
                                return (
                                    Response().error(f"停用 MCP 服务器 {request_schema.name} 超时。").__dict__
                                )
                            except Exception as e:
                                logger.error(traceback.format_exc())
                                return (
                                    Response()
                                    .error(f"停用 MCP 服务器 {request_schema.name} 失败: {str(e)}")
                                    .__dict__
                                )
                        return Response().ok(None, f"成功删除 MCP 服务器 {request_schema.name}").__dict__
                    else:
                        return Response().error(f"服务器 {request_schema.name} 不存在").__dict__

                except Exception as e:
                    logger.error(f"删除服务器时发生错误: {e}")
                    return Response().error(f"删除服务器失败: {str(e)}").__dict__

        except Exception as e:
            logger.error(traceback.format_exc())
            return Response().error(f"删除 MCP 服务器失败: {str(e)}").__dict__

    async def get_mcp_markets(self):
        try:
            init_db()

            with SessionLocal() as session:
                # 使用 Service 层获取市场条目
                market_items = MCPMarketService.get_all_market_items(session)

                # 转换为字典格式
                items = [item.dict() for item in market_items]

            return Response().ok(items).__dict__
        except Exception:
            logger.error(traceback.format_exc())
            return Response().error("获取本地市场数据失败").__dict__

    async def init_mcp_market_from_remote(self):
        """从远端初始化 MCP 市场数据到本地数据库"""
        try:
            body = await request.get_json()

            # 使用 Schema 验证请求数据
            try:
                request_schema = MCPMarketInitRequest(**body)
            except ValidationError as e:
                return Response().error(f"请求数据验证失败: {e}").__dict__

            # 从远端获取数据
            remote = f"https://api.soulter.top/astrbot/mcpservers?page={request_schema.page}&page_size={request_schema.page_size}"
            async with aiohttp.ClientSession() as session:
                async with session.get(remote) as response:
                    if response.status != 200:
                        return Response().error(f"远端请求失败: HTTP {response.status}").__dict__
                    payload = await response.json()
                    items = payload.get("data", [])

            init_db()
            try:
                with SessionLocal() as db_session:
                    # 清空现有数据
                    MCPMarketService.clear_all_market_items(db_session)

                    # 转换为 Schema 对象
                    market_items = []
                    for item in items:
                        try:
                            market_create = MCPMarketCreate(
                                name=item.get("name", ""),
                                description=item.get("desc", ""),
                                author=item.get("author", ""),
                                repo=item.get("repo", ""),
                                version=item.get("version", ""),
                                social_link=item.get("social_link", ""),
                                tags=item.get("tags", []),
                                logo=item.get("logo", ""),
                                stars=int(item.get("stars") or 0),
                                updated_at=int(item.get("updated_at") or 0),
                                pinned=bool(item.get("pinned", False)),
                            )
                            market_items.append(market_create)
                        except ValidationError as e:
                            logger.warning(f"跳过无效的市场条目 {item.get('name', 'unknown')}: {e}")
                            continue

                    # 批量创建市场条目
                    created_count = MCPMarketService.bulk_create_market_items(db_session, market_items)

            except Exception as e:
                logger.error(f"初始化本地 mcp_market 失败: {e}")
                return Response().error("写入本地数据库失败").__dict__

            return Response().ok(message=f"初始化完成，创建了 {created_count} 个市场条目").__dict__
        except Exception:
            logger.error(traceback.format_exc())
            return Response().error("初始化市场数据失败").__dict__

    async def test_mcp_connection(self):
        """
        测试 MCP 服务器连接
        """
        try:
            server_data = await request.json

            # 使用 Schema 验证请求数据
            try:
                request_schema = MCPServerTestRequest(**server_data)
            except ValidationError as e:
                return Response().error(f"请求数据验证失败: {e}").__dict__

            tools_name = await self.tool_mgr.test_mcp_server_connection(request_schema.mcp_server_config)
            return (
                Response().ok(data=tools_name, message="🎉 MCP 服务器可用！").__dict__
            )

        except Exception as e:
            logger.error(traceback.format_exc())
            return Response().error(f"测试 MCP 连接失败: {str(e)}").__dict__
