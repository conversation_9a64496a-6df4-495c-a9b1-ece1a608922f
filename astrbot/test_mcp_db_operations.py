#!/usr/bin/env python3
"""
MCP 数据库操作测试脚本
测试 MCP 服务器配置的数据库 CRUD 操作
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from astrbot.core import logger
from astrbot.core.db.orm import SessionLocal, init_db
from astrbot.core.db.models import MCPServer


def test_create_mcp_server():
    """测试创建 MCP 服务器配置"""
    print("🧪 测试创建 MCP 服务器配置...")
    
    try:
        init_db()
        
        test_servers = [
            {
                "name": "test_server_1",
                "active": True,
                "config": {
                    "command": "python",
                    "args": ["-m", "test_mcp_server"],
                    "cwd": "/path/to/server"
                }
            },
            {
                "name": "test_server_2", 
                "active": False,
                "config": {
                    "url": "http://localhost:8080/mcp",
                    "headers": {
                        "Authorization": "Bearer token123"
                    }
                }
            }
        ]
        
        with SessionLocal() as session:
            for server_data in test_servers:
                # 检查是否已存在
                existing = session.query(MCPServer).filter(MCPServer.name == server_data["name"]).first()
                if existing:
                    session.delete(existing)
                
                # 创建新服务器
                server = MCPServer(
                    name=server_data["name"],
                    active=server_data["active"],
                    config=server_data["config"]
                )
                session.add(server)
                print(f"  ✅ 创建服务器: {server_data['name']}")
            
            session.commit()
        
        print("✅ 创建测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试失败: {e}")
        return False


def test_read_mcp_servers():
    """测试读取 MCP 服务器配置"""
    print("\n🧪 测试读取 MCP 服务器配置...")
    
    try:
        with SessionLocal() as session:
            servers = session.query(MCPServer).all()
            
            print(f"  📊 找到 {len(servers)} 个服务器配置:")
            for server in servers:
                print(f"    - {server.name} (active: {server.active})")
                print(f"      配置: {server.config}")
                print(f"      创建时间: {server.created_at}")
                print(f"      更新时间: {server.updated_at}")
        
        print("✅ 读取测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 读取测试失败: {e}")
        return False


def test_update_mcp_server():
    """测试更新 MCP 服务器配置"""
    print("\n🧪 测试更新 MCP 服务器配置...")
    
    try:
        with SessionLocal() as session:
            # 查找测试服务器
            server = session.query(MCPServer).filter(MCPServer.name == "test_server_1").first()
            
            if not server:
                print("  ⚠️  测试服务器不存在，跳过更新测试")
                return True
            
            # 更新配置
            old_active = server.active
            server.active = not server.active
            server.config["updated"] = True
            server.config["new_field"] = "test_value"
            
            session.commit()
            
            print(f"  ✅ 更新服务器 {server.name}:")
            print(f"    - active: {old_active} -> {server.active}")
            print(f"    - 配置已更新: {server.config}")
        
        print("✅ 更新测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 更新测试失败: {e}")
        return False


def test_delete_mcp_server():
    """测试删除 MCP 服务器配置"""
    print("\n🧪 测试删除 MCP 服务器配置...")
    
    try:
        with SessionLocal() as session:
            # 查找测试服务器
            server = session.query(MCPServer).filter(MCPServer.name == "test_server_2").first()
            
            if not server:
                print("  ⚠️  测试服务器不存在，跳过删除测试")
                return True
            
            server_name = server.name
            session.delete(server)
            session.commit()
            
            print(f"  ✅ 删除服务器: {server_name}")
        
        print("✅ 删除测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 删除测试失败: {e}")
        return False


def test_query_operations():
    """测试查询操作"""
    print("\n🧪 测试查询操作...")
    
    try:
        with SessionLocal() as session:
            # 测试按名称查询
            server = session.query(MCPServer).filter(MCPServer.name == "test_server_1").first()
            if server:
                print(f"  ✅ 按名称查询成功: {server.name}")
            
            # 测试按状态查询
            active_servers = session.query(MCPServer).filter(MCPServer.active == True).all()
            print(f"  ✅ 活跃服务器数量: {len(active_servers)}")
            
            # 测试计数
            total_count = session.query(MCPServer).count()
            print(f"  ✅ 服务器总数: {total_count}")
        
        print("✅ 查询测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 查询测试失败: {e}")
        return False


def cleanup_test_data():
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        with SessionLocal() as session:
            # 删除所有测试服务器
            test_servers = session.query(MCPServer).filter(
                MCPServer.name.like("test_server_%")
            ).all()
            
            for server in test_servers:
                session.delete(server)
                print(f"  🗑️  删除测试服务器: {server.name}")
            
            session.commit()
        
        print("✅ 测试数据清理完成")
        return True
        
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 MCP 数据库操作测试")
    print("=" * 50)
    
    tests = [
        ("创建操作", test_create_mcp_server),
        ("读取操作", test_read_mcp_servers),
        ("更新操作", test_update_mcp_server),
        ("查询操作", test_query_operations),
        ("删除操作", test_delete_mcp_server),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 清理测试数据
    cleanup_test_data()
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP 数据库操作正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查数据库配置")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ 测试过程中发生未预期的错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
