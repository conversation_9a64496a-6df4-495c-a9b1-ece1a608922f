# SSO 配置示例

## 环境变量配置

为了使 SSO 功能正常工作，需要设置以下环境变量：

### 必需的环境变量

```bash
# OAuth2 客户端 ID
export OIDC_CLIENT_ID="your-oauth-client-id"

# OAuth2 客户端密钥
export OIDC_CLIENT_SECRET="your-oauth-client-secret"

# OAuth2 授权 URL
export OIDC_AUTHORIZATION_URL="https://your-oauth-server/oauth/authorize"

# OAuth2 令牌交换 URL
export OIDC_TOKEN_URL="https://your-oauth-server/oauth/token"

# OAuth2 用户信息 URL
export OIDC_USERINFO_URL="https://your-oauth-server/oauth/userinfo"

# OAuth2 回调 URL
export OIDC_REDIRECT_URL="http://localhost:6185/chat/auth/oauth/callback"

# RYJX IAM 相关配置
export IAM_API_BASE_URL="https://your-iam-server/api"
export SERVICE_DISCOVERY_URL="https://your-consul-server:8500"
```

### 配置文件方式

也可以在 `data/cmd_config.json` 中添加 OAuth 配置：

```json
{
  "dashboard": {
    "port": 6185,
    "host": "0.0.0.0",
    "jwt_secret": "your-jwt-secret"
  },
  "oauth": {
    "client_id": "your-oauth-client-id",
    "client_secret": "your-oauth-client-secret",
    "authorization_url": "https://your-oauth-server/oauth/authorize",
    "token_url": "https://your-oauth-server/oauth/token",
    "userinfo_url": "https://your-oauth-server/oauth/userinfo",
    "redirect_url": "http://localhost:6185/chat/auth/oauth/callback"
  },
  "iam": {
    "api_base_url": "https://your-iam-server/api",
    "service_discovery_url": "https://your-consul-server:8500"
  }
}
```

## OAuth 服务器要求

您的 OAuth 服务器需要支持以下端点：

### 1. 授权端点
- **URL**: `/oauth/authorize`
- **方法**: GET
- **参数**: 
  - `client_id`: 客户端 ID
  - `redirect_uri`: 回调 URL
  - `response_type`: 固定为 "code"
  - `scope`: "openid profile email"
  - `state`: CSRF 保护参数

### 2. 令牌交换端点
- **URL**: `/oauth/token`
- **方法**: POST
- **参数**:
  - `grant_type`: 固定为 "authorization_code"
  - `code`: 授权码
  - `client_id`: 客户端 ID
  - `client_secret`: 客户端密钥
- **响应**:
  ```json
  {
    "access_token": "access-token-value",
    "refresh_token": "refresh-token-value",
    "token_type": "Bearer",
    "expires_in": 3600
  }
  ```

### 3. 用户信息端点
- **URL**: `/oauth/userinfo`
- **方法**: GET
- **头部**: `Authorization: Bearer <access_token>`
- **响应**:
  ```json
  {
    "id": "user-unique-id",
    "username": "username",
    "email": "<EMAIL>",
    "name": "Display Name",
    "preferred_username": "username"
  }
  ```

## 部署注意事项

1. **HTTPS**: 生产环境必须使用 HTTPS
2. **回调 URL**: 确保 OAuth 服务器允许配置的回调 URL
3. **域名**: 确保前端应用域名与回调 URL 匹配
4. **CORS**: 如果前后端分离部署，注意 CORS 配置

## 测试配置

可以使用以下命令测试 OAuth 配置：

```bash
# 测试配置端点
curl http://localhost:6185/chat-api/api/oauth/config

# 应该返回类似以下的响应：
{
  "client_id": "your-client-id",
  "redirect_uri": "http://localhost:6185/chat/auth/oauth/callback",
  "authorize_url": "https://your-oauth-server/oauth/authorize",
  "scope": "openid profile email",
  "response_type": "code"
}
```

## 故障排除

### 常见问题

1. **配置端点返回空值**
   - 检查环境变量是否正确设置
   - 确认变量名拼写正确

2. **令牌交换失败**
   - 检查客户端 ID 和密钥是否正确
   - 确认令牌端点 URL 是否可访问

3. **用户信息获取失败**
   - 检查用户信息端点 URL
   - 确认访问令牌是否有效

4. **权限检查失败**
   - 当前实现允许所有 SSO 用户访问
   - 可根据需要修改权限检查逻辑

### 日志查看

查看 AstrBot 日志以获取详细的错误信息：

```bash
# 查看最新日志
tail -f data/logs/astrbot.log

# 搜索 OAuth 相关日志
grep -i oauth data/logs/astrbot.log
```
