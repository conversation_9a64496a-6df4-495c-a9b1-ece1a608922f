import os
import logging
from pathlib import Path
from typing import Optional, Literal
try:
    from pydantic_settings import BaseSettings
    from pydantic import Field, field_validator
except ImportError:
    # 兼容旧版本 Pydantic
    from pydantic import BaseSettings, Field, field_validator
from dotenv import load_dotenv

logger = logging.getLogger("astrbot")


class EnvironmentConfig(BaseSettings):
    """环境配置管理器

    使用 Pydantic Settings 管理环境变量配置
    通过 APP_ENV 环境变量读取对应的 .env 配置文件
    支持的环境：local, dev, beta, prod
    """

    # 基础环境配置
    APP_ENV: Literal['local', 'dev', 'beta', 'prod'] = Field(
        default='local',
        env='APP_ENV',
        description="应用环境"
    )

    # 数据库配置
    DATABASE_TYPE: str = Field(
        default='sqlite',
        env='DATABASE_TYPE',
        description="数据库类型"
    )

    DATABASE_URL: str = Field(
        default='*********************************************/staff_sso',
        env='DATABASE_URL',
        description="数据库连接URL"
    )

    # Web 配置
    WEB_HOST: str = Field(
        default='127.0.0.1',
        env='WEB_HOST',
        description="Web服务器主机"
    )

    WEB_PORT: int = Field(
        default=6185,
        env='WEB_PORT',
        description="Web服务器端口"
    )

    # 日志配置
    LOG_LEVEL: str = Field(
        default='INFO',
        env='LOG_LEVEL',
        description="日志级别"
    )

    LOG_FILE: str = Field(
        default='logs/astrbot.log',
        env='LOG_FILE',
        description="日志文件路径"
    )

    # 其他配置
    DEBUG: bool = Field(
        default=False,
        env='DEBUG',
        description="是否开启调试模式"
    )

    ENABLE_METRICS: bool = Field(
        default=True,
        env='ENABLE_METRICS',
        description="是否启用指标收集"
    )

    ENABLE_WEB_CONSOLE: bool = Field(
        default=True,
        env='ENABLE_WEB_CONSOLE',
        description="是否启用Web控制台"
    )

    # OAuth2 配置
    OIDC_CLIENT_ID: str
    OIDC_CLIENT_SECRET: str
    OIDC_USERINFO_URL: str
    OIDC_ISSUER: str
    OIDC_AUTHORIZATION_URL: str
    OIDC_REDIRECT_URL: str
    OIDC_LOGOUT_URL: str
    OIDC_INTROSPECTION_URL: str
    OIDC_JWKS_URL: str
    OIDC_TOKEN_URL: str

    # 权限系统配置
    PERMISSION_ROOT: str
    PERMISSION_APP: str
    PERMISSION_ALL: str
    
    # IAM 配置
    IAM_API_BASE_URL: str = Field(description="IAM API 基础URL")
    IAM_API_OUTER_HOST: str
    IAM_API_INTERNAL_HOST: str = Field(description="IAM API 内部主机")
    SERVICE_DISCOVERY_URL: str = Field(description="服务发现URL")

    # Redis 缓存配置
    REDIS_HOST: str = Field(
        default='localhost',
        env='REDIS_HOST',
        description="Redis 服务器主机"
    )

    REDIS_PORT: int = Field(
        default=6379,
        env='REDIS_PORT',
        description="Redis 服务器端口"
    )

    REDIS_PASSWORD: Optional[str] = Field(
        default=None,
        env='REDIS_PASSWORD',
        description="Redis 密码"
    )

    REDIS_DB: int = Field(
        default=0,
        env='REDIS_DB',
        description="Redis 数据库编号"
    )

    REDIS_ENABLED: bool = Field(
        default=False,
        env='REDIS_ENABLED',
        description="是否启用 Redis 缓存"
    )

    REDIS_TTL: int = Field(
        default=3600,
        env='REDIS_TTL',
        description="Redis 缓存过期时间（秒）"
    )

    class Config:
        # 环境变量文件配置
        env_file = None  # 动态设置
        env_file_encoding = 'utf-8'
        case_sensitive = False

    def __init__(self, **kwargs):
        # 在初始化前加载环境配置文件
        self._load_env_config()
        super().__init__(**kwargs)

    def _load_env_config(self):
        """加载环境配置文件"""
        app_env = os.getenv('APP_ENV', 'local')
        project_root = Path(__file__).parent.parent.parent.parent
        config_dir = project_root / 'config'
        env_file = config_dir / f'.env.{app_env}'

        if env_file.exists():
            load_dotenv(env_file)
            logger.info(f"已加载环境配置: {env_file}")
        else:
            logger.warning(f"环境配置文件不存在: {env_file}")
            # 尝试加载默认的 .env 文件
            default_env = config_dir / '.env'
            if default_env.exists():
                load_dotenv(default_env)
                logger.info(f"已加载默认环境配置: {default_env}")

    @field_validator('LOG_LEVEL')
    @classmethod
    def validate_log_level(cls, v):
        """验证日志级别"""
        valid_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        if v.upper() not in valid_levels:
            raise ValueError(f'日志级别必须是以下之一: {valid_levels}')
        return v.upper()

    @field_validator('WEB_PORT')
    @classmethod
    def validate_web_port(cls, v):
        """验证端口号"""
        if not (1 <= v <= 65535):
            raise ValueError('端口号必须在 1-65535 之间')
        return v

    # 环境判断属性
    @property
    def is_local(self) -> bool:
        """是否为本机环境"""
        return self.APP_ENV == 'local'

    @property
    def is_dev(self) -> bool:
        """是否为开发环境"""
        return self.APP_ENV == 'dev'

    @property
    def is_beta(self) -> bool:
        """是否为线上预览环境"""
        return self.APP_ENV == 'beta'

    @property
    def is_prod(self) -> bool:
        """是否为正式环境"""
        return self.APP_ENV == 'prod'

    # 兼容性方法
    def get(self, key: str, default: Optional[str] = None) -> Optional[str]:
        """获取环境变量（兼容旧接口）"""
        return os.getenv(key, default)

    def get_bool(self, key: str, default: bool = False) -> bool:
        """获取布尔类型的环境变量（兼容旧接口）"""
        value = self.get(key)
        if value is None:
            return default
        return value.lower() in ('true', '1', 'yes', 'on')

    def get_int(self, key: str, default: int = 0) -> int:
        """获取整数类型的环境变量（兼容旧接口）"""
        value = self.get(key)
        if value is None:
            return default
        try:
            return int(value)
        except ValueError:
            logger.warning(f"环境变量 {key} 的值 '{value}' 不是有效的整数，使用默认值 {default}")
            return default

# 全局环境配置实例
env_config = EnvironmentConfig()
