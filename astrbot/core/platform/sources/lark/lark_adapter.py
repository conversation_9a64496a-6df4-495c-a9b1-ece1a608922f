import base64
import asyncio
import json
import re
import uuid
import time
import astrbot.api.message_components as Comp

from astrbot.api.platform import (
    Platform,
    AstrBotMessage,
    MessageMember,
    MessageType,
    PlatformMetadata,
)
from astrbot.api.event import Message<PERSON>hain
from astrbot.core.platform.astr_message_event import MessageSesion
from .lark_event import LarkMessageEvent
from ...register import register_platform_adapter
from astrbot import logger
import lark_oapi as lark
from lark_oapi.api.im.v1 import *
from lark_oapi.api.contact.v3 import *


@register_platform_adapter("lark", "飞书机器人官方 API 适配器")
class LarkPlatformAdapter(Platform):
    def __init__(
        self, platform_config: dict, platform_settings: dict, event_queue: asyncio.Queue
    ) -> None:
        super().__init__(event_queue)

        self.config = platform_config

        self.unique_session = platform_settings["unique_session"]

        self.appid = platform_config["app_id"]
        self.appsecret = platform_config["app_secret"]
        self.domain = platform_config.get("domain", lark.FEISHU_DOMAIN)
        self.bot_name = platform_config.get("lark_bot_name", "astrbot")

        if not self.bot_name:
            logger.warning("未设置飞书机器人名称，@ 机器人可能得不到回复。")

        async def on_msg_event_recv(event: lark.im.v1.P2ImMessageReceiveV1):
            await self.convert_msg(event)

        def do_v2_msg_event(event: lark.im.v1.P2ImMessageReceiveV1):
            asyncio.create_task(on_msg_event_recv(event))

        self.event_handler = (
            lark.EventDispatcherHandler.builder("", "")
            .register_p2_im_message_receive_v1(do_v2_msg_event)
            .build()
        )

        self.client = lark.ws.Client(
            app_id=self.appid,
            app_secret=self.appsecret,
            log_level=lark.LogLevel.ERROR,
            domain=self.domain,
            event_handler=self.event_handler,
        )

        self.lark_api = (
            lark.Client.builder().app_id(self.appid).app_secret(self.appsecret).build()
        )

        # 用户信息缓存，避免重复调用API
        self.user_info_cache = {}

        # 权限检查标记
        self.has_user_info_permission = None  # None: 未检查, True: 有权限, False: 无权限

    async def get_user_info(self, open_id: str, user_name: str = None) -> dict:
        """通过飞书API获取用户真实信息

        Args:
            open_id: 用户的 open_id
            user_name: 从消息事件中获取的用户名（备用）

        Returns:
            dict: 包含用户信息的字典，包括 name, email, mobile 等
        """
        # 检查缓存（5分钟过期）
        current_time = time.time()
        if open_id in self.user_info_cache:
            cached_data = self.user_info_cache[open_id]
            if current_time - cached_data.get("cached_time", 0) < 300:  # 5分钟
                return cached_data

        try:
            # 使用飞书通讯录API获取用户信息
            # 需要应用具有 contact:user.base:readonly 权限
            request = GetUserRequest.builder().user_id_type("open_id").user_id(open_id).build()
            response = await self.lark_api.contact.v3.user.aget(request)

            if response.success():
                user = response.data.user
                user_info = {
                    "open_id": open_id,
                    "name": user.name or user_name or f"用户{open_id[-6:]}",
                    "en_name": getattr(user, 'en_name', ''),
                    "email": getattr(user, 'email', ''),
                    "mobile": getattr(user, 'mobile', ''),
                    "department_ids": getattr(user, 'department_ids', []),
                    "avatar": getattr(user, 'avatar', {}),
                    "employee_no": getattr(user, 'employee_no', ''),
                    "employee_type": getattr(user, 'employee_type', 0),
                    "status": getattr(user, 'status', {}),
                    "cached_time": current_time,
                    "from_api": True,  # 标记为API获取
                }

                # 缓存用户信息
                self.user_info_cache[open_id] = user_info
                logger.debug(f"从飞书API获取用户信息成功: {user_info['name']} ({open_id})")
                return user_info
            else:
                # 处理不同的错误码
                if response.code == 41050:
                    logger.warning(f"飞书应用缺少获取用户信息权限 (错误码: {response.code})")
                    logger.info("请在飞书开放平台为应用添加以下权限:")
                    logger.info("- contact:user.base:readonly (获取用户基本信息)")
                    logger.info("- contact:user.employee_id:readonly (通过员工ID获取用户信息)")
                elif response.code == 99991663:
                    logger.warning(f"用户不存在或已被删除: {open_id}")
                elif response.code == 230002:
                    logger.warning(f"用户不在应用可见范围内: {open_id}")
                else:
                    logger.warning(f"获取飞书用户信息失败({response.code}): {response.msg}")

                # 返回默认信息，使用提供的用户名或生成友好显示名
                display_name = user_name or f"用户{open_id[-6:]}" if len(open_id) > 6 else open_id[:8]
                default_info = {
                    "open_id": open_id,
                    "name": display_name,
                    "en_name": "",
                    "email": "",
                    "mobile": "",
                    "department_ids": [],
                    "avatar": {},
                    "employee_no": "",
                    "employee_type": 0,
                    "status": {},
                    "cached_time": current_time,
                    "from_api": False,  # 标记为默认信息
                    "error_code": response.code,
                    "error_msg": response.msg,
                }
                self.user_info_cache[open_id] = default_info
                return default_info

        except Exception as e:
            logger.error(f"调用飞书API获取用户信息异常: {e}")
            # 返回默认信息
            display_name = user_name or f"用户{open_id[-6:]}" if len(open_id) > 6 else open_id[:8]
            default_info = {
                "open_id": open_id,
                "name": display_name,
                "en_name": "",
                "email": "",
                "mobile": "",
                "department_ids": [],
                "avatar": {},
                "employee_no": "",
                "employee_type": 0,
                "status": {},
                "cached_time": current_time,
                "from_api": False,  # 标记为默认信息
                "error": str(e),
            }
            self.user_info_cache[open_id] = default_info
            return default_info
        """获取用户信息

        注意：飞书获取详细用户信息需要 user_access_token（用户授权），
        机器人场景下通常无法获取，所以使用基础信息和友好的显示名

        Args:
            open_id: 用户的 open_id
            user_name: 从消息事件中获取的用户名（如果有的话）

        Returns:
            dict: 包含用户信息的字典
        """
        # 检查缓存（30分钟过期）
        current_time = time.time()
        # if open_id in self.user_info_cache:
        #     cached_data = self.user_info_cache[open_id]
        #     if current_time - cached_data.get("cached_time", 0) < 1800:  # 30分钟
        #         return cached_data

        # 生成友好的显示名
        if user_name:
            display_name = user_name
        else:
            # 使用 open_id 的后6位作为显示名
            display_name = f"用户{open_id[-6:]}" if len(open_id) > 6 else open_id[:8]

        # 由于机器人获取详细用户信息需要用户授权，这里使用基础信息
        user_info = {
            "open_id": open_id,
            "name": display_name,
            "en_name": "",
            "email": "",
            "mobile": "",
            "department_ids": [],
            "avatar": {},
            "employee_no": "",
            "employee_type": 0,
            "status": {},
            "cached_time": current_time,
            "is_basic_info": True,  # 标记为基础信息
        }

        # 缓存用户信息
        self.user_info_cache[open_id] = user_info
        logger.debug(f"使用基础用户信息: {display_name} ({open_id})")

        return user_info

    def _cleanup_expired_cache(self):
        """清理过期的用户信息缓存"""
        current_time = time.time()
        expired_keys = []

        for open_id, user_info in self.user_info_cache.items():
            if current_time - user_info.get("cached_time", 0) > 1800:  # 30分钟过期
                expired_keys.append(open_id)

        for key in expired_keys:
            del self.user_info_cache[key]

        if expired_keys:
            logger.debug(f"清理了 {len(expired_keys)} 个过期的用户信息缓存")

    async def send_by_session(
        self, session: MessageSesion, message_chain: MessageChain
    ):
        res = await LarkMessageEvent._convert_to_lark(message_chain, self.lark_api)
        wrapped = {
            "zh_cn": {
                "title": "",
                "content": res,
            }
        }

        if session.message_type == MessageType.GROUP_MESSAGE:
            id_type = "chat_id"
            if "%" in session.session_id:
                session.session_id = session.session_id.split("%")[1]
        else:
            id_type = "open_id"

        request = (
            CreateMessageRequest.builder()
            .receive_id_type(id_type)
            .request_body(
                CreateMessageRequestBody.builder()
                .receive_id(session.session_id)
                .content(json.dumps(wrapped))
                .msg_type("post")
                .uuid(str(uuid.uuid4()))
                .build()
            )
            .build()
        )

        response = await self.lark_api.im.v1.message.acreate(request)

        if not response.success():
            logger.error(f"发送飞书消息失败({response.code}): {response.msg}")

        await super().send_by_session(session, message_chain)

    def meta(self) -> PlatformMetadata:
        return PlatformMetadata(
            name="lark",
            description="飞书机器人官方 API 适配器",
            id=self.config.get("id"),
        )

    async def convert_msg(self, event: lark.im.v1.P2ImMessageReceiveV1):
        # 定期清理过期缓存（每100次消息处理清理一次）
        if len(self.user_info_cache) > 0 and len(self.user_info_cache) % 100 == 0:
            self._cleanup_expired_cache()

        message = event.event.message
        abm = AstrBotMessage()
        abm.timestamp = int(message.create_time) / 1000
        abm.message = []
        abm.type = (
            MessageType.GROUP_MESSAGE
            if message.chat_type == "group"
            else MessageType.FRIEND_MESSAGE
        )
        if message.chat_type == "group":
            abm.group_id = message.chat_id
        abm.self_id = self.bot_name
        abm.message_str = ""

        at_list = {}
        if message.mentions:
            for m in message.mentions:
                # 获取被@用户的信息，使用飞书提供的用户名
                # mentioned_user_info = await self.get_user_info(m.id.open_id, m.name)
                mentioned_user_info = await self.get_user_info(m.id.open_id)

                at_list[m.key] = Comp.At(qq=m.id.open_id, name=mentioned_user_info["name"])
                if m.name == self.bot_name:
                    abm.self_id = m.id.open_id

        content_json_b = json.loads(message.content)

        if message.message_type == "text":
            message_str_raw = content_json_b["text"]  # 带有 @ 的消息
            at_pattern = r"(@_user_\d+)"  # 可以根据需求修改正则
            # at_users = re.findall(at_pattern, message_str_raw)
            # 拆分文本，去掉AT符号部分
            parts = re.split(at_pattern, message_str_raw)
            for i in range(len(parts)):
                s = parts[i].strip()
                if not s:
                    continue
                if s in at_list:
                    abm.message.append(at_list[s])
                else:
                    abm.message.append(Comp.Plain(parts[i].strip()))
        elif message.message_type == "post":
            _ls = []

            content_ls = content_json_b.get("content", [])
            for comp in content_ls:
                if isinstance(comp, list):
                    _ls.extend(comp)
                elif isinstance(comp, dict):
                    _ls.append(comp)
            content_json_b = _ls
        elif message.message_type == "image":
            content_json_b = [
                {"tag": "img", "image_key": content_json_b["image_key"], "style": []}
            ]

        if message.message_type in ("post", "image"):
            for comp in content_json_b:
                if comp["tag"] == "at":
                    abm.message.append(at_list[comp["user_id"]])
                elif comp["tag"] == "text" and comp["text"].strip():
                    abm.message.append(Comp.Plain(comp["text"].strip()))
                elif comp["tag"] == "img":
                    image_key = comp["image_key"]
                    request = (
                        GetMessageResourceRequest.builder()
                        .message_id(message.message_id)
                        .file_key(image_key)
                        .type("image")
                        .build()
                    )
                    response = await self.lark_api.im.v1.message_resource.aget(request)
                    if not response.success():
                        logger.error(f"无法下载飞书图片: {image_key}")
                    image_bytes = response.file.read()
                    image_base64 = base64.b64encode(image_bytes).decode()
                    abm.message.append(Comp.Image.fromBase64(image_base64))

        for comp in abm.message:
            if isinstance(comp, Comp.Plain):
                abm.message_str += comp.text
        abm.message_id = message.message_id
        abm.raw_message = message

        # 获取用户信息
        open_id = event.event.sender.sender_id.open_id
        # 尝试从消息发送者信息中获取用户名
        sender_name = getattr(event.event.sender, 'sender_name', None)
        user_info = await self.get_user_info(open_id)

        abm.sender = MessageMember(
            user_id=open_id,
            nickname=user_info["name"],
        )
        # 独立会话
        if not self.unique_session:
            if abm.type == MessageType.GROUP_MESSAGE:
                abm.session_id = abm.group_id
            else:
                abm.session_id = abm.sender.user_id
        else:
            if abm.type == MessageType.GROUP_MESSAGE:
                abm.session_id = f"{abm.sender.user_id}%{abm.group_id}"  # 也保留群组id
            else:
                abm.session_id = abm.sender.user_id

        logger.debug(abm)
        await self.handle_msg(abm)

    async def handle_msg(self, abm: AstrBotMessage):
        event = LarkMessageEvent(
            message_str=abm.message_str,
            message_obj=abm,
            platform_meta=self.meta(),
            session_id=abm.session_id,
            bot=self.lark_api,
        )

        self._event_queue.put_nowait(event)

    async def run(self):
        # self.client.start()
        await self.client._connect()

    async def terminate(self):
        await self.client._disconnect()
        logger.info("飞书(Lark) 适配器已被优雅地关闭")

    def get_client(self) -> lark.Client:
        return self.client
