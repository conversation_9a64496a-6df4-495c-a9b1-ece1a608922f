from ryjx.iam.oauth2_client import OAuth2Client
from ryjx.iam.oidc_client import outer_to_inner
from ryjx.common.service_client import ServiceClient
from astrbot.core.config.env_config import env_config

# 使用 Pydantic Settings 配置
_service_client = ServiceClient(
    base_url=env_config.IAM_API_BASE_URL or '',
    consul_server=env_config.SERVICE_DISCOVERY_URL or ''
)

oauth2_client = OAuth2Client(
    client_id=env_config.OIDC_CLIENT_ID or '',
    client_secret=env_config.OIDC_CLIENT_SECRET or '',
    token_endpoint=outer_to_inner(env_config.OIDC_TOKEN_URL or ''),
    userinfo_endpoint=outer_to_inner(env_config.OIDC_USERINFO_URL or ''),
    service_client=_service_client
)