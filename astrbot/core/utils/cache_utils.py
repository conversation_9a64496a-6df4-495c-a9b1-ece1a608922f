"""
Redis 缓存工具类
提供统一的缓存接口，支持 Redis 和内存缓存
"""

import json
import hashlib
from typing import Optional, Any, Dict
from datetime import datetime, timedelta
from astrbot.core.config.env_config import env_config
from astrbot.core import logger

# 尝试导入 Redis 相关库
try:
    import redis.asyncio as redis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logger.warning("Redis 库未安装，将使用内存缓存")


class CacheManager:
    """缓存管理器，支持 Redis 和内存缓存"""

    def __init__(self):
        self._redis_client: Optional[redis.Redis] = None
        self._memory_cache: Dict[str, Dict[str, Any]] = {}
        self._initialized = False

    async def initialize(self):
        """初始化缓存管理器"""
        if self._initialized:
            return

        if env_config.REDIS_ENABLED and REDIS_AVAILABLE:
            try:
                # 创建 Redis 连接
                self._redis_client = redis.Redis(
                    host=env_config.REDIS_HOST,
                    port=env_config.REDIS_PORT,
                    password=env_config.REDIS_PASSWORD,
                    db=env_config.REDIS_DB,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5
                )

                # 测试连接
                await self._redis_client.ping()
                logger.info(f"Redis 缓存已启用: {env_config.REDIS_HOST}:{env_config.REDIS_PORT}")

            except Exception as e:
                logger.error(f"Redis 连接失败，将使用内存缓存: {e}")
                self._redis_client = None
        else:
            logger.info("使用内存缓存")

        self._initialized = True

    def _generate_key(self, prefix: str, identifier: str) -> str:
        """生成缓存键"""
        # 对标识符进行 MD5 哈希，避免键过长
        hash_id = hashlib.md5(identifier.encode()).hexdigest()
        return f"astrbot:{prefix}:{hash_id}"

    async def get(self, prefix: str, identifier: str) -> Optional[Any]:
        """获取缓存数据"""
        await self.initialize()

        key = self._generate_key(prefix, identifier)

        try:
            if self._redis_client:
                # 使用 Redis 缓存
                data = await self._redis_client.get(key)
                if data:
                    return json.loads(data)
            else:
                # 使用内存缓存
                if key in self._memory_cache:
                    cache_item = self._memory_cache[key]
                    # 检查是否过期
                    if datetime.now() < cache_item['expires_at']:
                        return cache_item['data']
                    else:
                        # 删除过期数据
                        del self._memory_cache[key]

        except Exception as e:
            logger.error(f"获取缓存失败: {e}")

        return None

    async def set(self, prefix: str, identifier: str, data: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存数据"""
        await self.initialize()

        key = self._generate_key(prefix, identifier)
        ttl = ttl or env_config.REDIS_TTL

        try:
            if self._redis_client:
                # 使用 Redis 缓存
                json_data = json.dumps(data, ensure_ascii=False)
                await self._redis_client.setex(key, ttl, json_data)
            else:
                # 使用内存缓存
                expires_at = datetime.now() + timedelta(seconds=ttl)
                self._memory_cache[key] = {
                    'data': data,
                    'expires_at': expires_at
                }

            return True

        except Exception as e:
            logger.error(f"设置缓存失败: {e}")
            return False

    async def delete(self, prefix: str, identifier: str) -> bool:
        """删除缓存数据"""
        await self.initialize()

        key = self._generate_key(prefix, identifier)

        try:
            if self._redis_client:
                # 使用 Redis 缓存
                await self._redis_client.delete(key)
            else:
                # 使用内存缓存
                if key in self._memory_cache:
                    del self._memory_cache[key]

            return True

        except Exception as e:
            logger.error(f"删除缓存失败: {e}")
            return False

    async def exists(self, prefix: str, identifier: str) -> bool:
        """检查缓存是否存在"""
        await self.initialize()

        key = self._generate_key(prefix, identifier)

        try:
            if self._redis_client:
                # 使用 Redis 缓存
                return await self._redis_client.exists(key) > 0
            else:
                # 使用内存缓存
                if key in self._memory_cache:
                    cache_item = self._memory_cache[key]
                    # 检查是否过期
                    if datetime.now() < cache_item['expires_at']:
                        return True
                    else:
                        # 删除过期数据
                        del self._memory_cache[key]

        except Exception as e:
            logger.error(f"检查缓存存在性失败: {e}")

        return False

    async def clear_prefix(self, prefix: str) -> bool:
        """清除指定前缀的所有缓存"""
        await self.initialize()

        try:
            if self._redis_client:
                # 使用 Redis 缓存
                pattern = f"astrbot:{prefix}:*"
                keys = await self._redis_client.keys(pattern)
                if keys:
                    await self._redis_client.delete(*keys)
            else:
                # 使用内存缓存
                prefix_pattern = f"astrbot:{prefix}:"
                keys_to_delete = [key for key in self._memory_cache.keys() if key.startswith(prefix_pattern)]
                for key in keys_to_delete:
                    del self._memory_cache[key]

            return True

        except Exception as e:
            logger.error(f"清除前缀缓存失败: {e}")
            return False

    async def close(self):
        """关闭缓存连接"""
        if self._redis_client:
            await self._redis_client.close()
            self._redis_client = None
        self._memory_cache.clear()
        self._initialized = False


# 全局缓存管理器实例
cache_manager = CacheManager()


def cache_key_for_user_info(access_token: str) -> str:
    """为用户信息生成缓存键标识符"""
    return f"token:{access_token}"


async def get_cached_user_info(access_token: str) -> Optional[Dict[str, Any]]:
    """获取缓存的用户信息"""
    return await cache_manager.get("user_info", cache_key_for_user_info(access_token))


async def set_cached_user_info(access_token: str, user_info: Dict[str, Any], ttl: Optional[int] = None) -> bool:
    """设置用户信息缓存"""
    return await cache_manager.set("user_info", cache_key_for_user_info(access_token), user_info, ttl)


async def delete_cached_user_info(access_token: str) -> bool:
    """删除用户信息缓存"""
    return await cache_manager.delete("user_info", cache_key_for_user_info(access_token))


async def clear_all_user_info_cache() -> bool:
    """清除所有用户信息缓存"""
    return await cache_manager.clear_prefix("user_info")


# 缓存装饰器
def cache_result(prefix: str, ttl: Optional[int] = None, key_func: Optional[callable] = None):
    """
    缓存装饰器

    Args:
        prefix: 缓存前缀
        ttl: 缓存过期时间（秒）
        key_func: 生成缓存键的函数，接收函数参数，返回字符串
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认使用函数名和参数生成键
                args_str = "_".join(str(arg) for arg in args)
                kwargs_str = "_".join(f"{k}={v}" for k, v in kwargs.items())
                cache_key = f"{func.__name__}_{args_str}_{kwargs_str}"

            # 尝试从缓存获取
            cached_result = await cache_manager.get(prefix, cache_key)
            if cached_result is not None:
                logger.debug(f"缓存命中: {prefix}:{cache_key}")
                return cached_result

            # 执行原函数
            result = await func(*args, **kwargs)

            # 缓存结果
            if result is not None:
                await cache_manager.set(prefix, cache_key, result, ttl)
                logger.debug(f"缓存设置: {prefix}:{cache_key}")

            return result

        return wrapper
    return decorator