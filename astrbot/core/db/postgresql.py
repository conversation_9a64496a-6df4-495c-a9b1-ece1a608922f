import logging
import time
import json
from typing import Tuple, List, Dict, Any, Optional
from urllib.parse import urlparse
import psycopg2
from psycopg2.extras import RealDictCursor
from psycopg2 import sql
from astrbot.core.db.po import Platform, Stats, LLMHistory, ATRIVision, Conversation
from .base import BaseDatabase

logger = logging.getLogger("astrbot")


class PostgreSQLDatabase(BaseDatabase):
    def __init__(self, database_url: str) -> None:
        super().__init__()
        self.database_url = database_url
        
        # 解析数据库URL
        parsed = urlparse(database_url)
        self.db_config = {
            'host': parsed.hostname,
            'port': parsed.port or 5432,
            'database': parsed.path[1:] if parsed.path else 'astrbot',
            'user': parsed.username,
            'password': parsed.password
        }
        
        # 初始化数据库连接和表结构
        self._init_database()
    
    def _get_connection(self):
        """获取数据库连接"""
        try:
            conn = psycopg2.connect(**self.db_config)
            conn.autocommit = True
            return conn
        except Exception as e:
            logger.error(f"PostgreSQL 连接失败: {e}")
            raise
    
    def _init_database(self):
        """初始化数据库表结构"""
        init_sql = """
        -- 平台统计表
        CREATE TABLE IF NOT EXISTS platform (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            count INTEGER NOT NULL DEFAULT 0,
            timestamp BIGINT NOT NULL
        );
        
        -- 命令统计表
        CREATE TABLE IF NOT EXISTS command (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            count INTEGER NOT NULL DEFAULT 0,
            timestamp BIGINT NOT NULL
        );
        
        -- LLM 统计表
        CREATE TABLE IF NOT EXISTS llm (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            count INTEGER NOT NULL DEFAULT 0,
            timestamp BIGINT NOT NULL
        );
        
        -- LLM 历史记录表
        CREATE TABLE IF NOT EXISTS llm_history (
            id SERIAL PRIMARY KEY,
            provider_type VARCHAR(255) NOT NULL,
            session_id VARCHAR(255) NOT NULL,
            content TEXT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(provider_type, session_id)
        );
        
        -- Web聊天对话表
        CREATE TABLE IF NOT EXISTS webchat_conversation (
            id SERIAL PRIMARY KEY,
            user_id VARCHAR(255) NOT NULL,
            cid VARCHAR(255) NOT NULL,
            history TEXT NOT NULL DEFAULT '[]',
            created_at BIGINT NOT NULL,
            updated_at BIGINT NOT NULL,
            title TEXT,
            persona_id TEXT,
            UNIQUE(user_id, cid)
        );
        
        -- ATRI 视觉数据表
        CREATE TABLE IF NOT EXISTS atri_vision (
            id VARCHAR(255) PRIMARY KEY,
            url_or_path TEXT NOT NULL,
            caption TEXT,
            is_meme BOOLEAN DEFAULT FALSE,
            keywords TEXT,
            platform_name VARCHAR(255),
            session_id VARCHAR(255),
            sender_nickname VARCHAR(255),
            timestamp BIGINT NOT NULL
        );
        
        -- 创建索引
        CREATE INDEX IF NOT EXISTS idx_platform_timestamp ON platform(timestamp);
        CREATE INDEX IF NOT EXISTS idx_command_timestamp ON command(timestamp);
        CREATE INDEX IF NOT EXISTS idx_llm_timestamp ON llm(timestamp);
        CREATE INDEX IF NOT EXISTS idx_webchat_conversation_user_id ON webchat_conversation(user_id);
        CREATE INDEX IF NOT EXISTS idx_webchat_conversation_updated_at ON webchat_conversation(updated_at);
        CREATE INDEX IF NOT EXISTS idx_atri_vision_timestamp ON atri_vision(timestamp);

        -- MCP 市场表
        CREATE TABLE IF NOT EXISTS mcp_market (
            name VARCHAR(255) PRIMARY KEY,
            description TEXT,
            author VARCHAR(255),
            repo TEXT,
            version VARCHAR(64),
            social_link TEXT,
            tags TEXT, -- JSON 数组字符串
            logo TEXT,
            stars INTEGER DEFAULT 0,
            updated_at BIGINT DEFAULT 0,
            pinned BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        conn = self._get_connection()
        try:
            with conn.cursor() as cursor:
                cursor.execute(init_sql)
            logger.info("PostgreSQL 数据库表结构初始化完成")
        except Exception as e:
            logger.error(f"PostgreSQL 数据库初始化失败: {e}")
            raise
        finally:
            conn.close()
    
    def _execute_sql(self, sql_query: str, params: Tuple = None, fetch: bool = False):
        """执行SQL语句"""
        conn = self._get_connection()
        try:
            with conn.cursor(cursor_factory=RealDictCursor) as cursor:
                if params:
                    cursor.execute(sql_query, params)
                else:
                    cursor.execute(sql_query)
                
                if fetch:
                    return cursor.fetchall()
                return cursor.rowcount
        except Exception as e:
            logger.error(f"SQL执行失败: {e}, SQL: {sql_query}")
            raise
        finally:
            conn.close()
    
    def insert_platform_metrics(self, metrics: dict):
        """插入平台指标"""
        for k, v in metrics.items():
            self._execute_sql(
                "INSERT INTO platform(name, count, timestamp) VALUES (%s, %s, %s)",
                (k, v, int(time.time()))
            )
    
    def insert_plugin_metrics(self, metrics: dict):
        """插入插件指标（暂未实现）"""
        pass
    
    def insert_command_metrics(self, metrics: dict):
        """插入命令指标"""
        for k, v in metrics.items():
            self._execute_sql(
                "INSERT INTO command(name, count, timestamp) VALUES (%s, %s, %s)",
                (k, v, int(time.time()))
            )
    
    def insert_llm_metrics(self, metrics: dict):
        """插入LLM指标"""
        for k, v in metrics.items():
            self._execute_sql(
                "INSERT INTO llm(name, count, timestamp) VALUES (%s, %s, %s)",
                (k, v, int(time.time()))
            )
    
    def update_llm_history(self, session_id: str, content: str, provider_type: str):
        """更新LLM历史记录"""
        # 先尝试更新
        updated = self._execute_sql(
            "UPDATE llm_history SET content = %s WHERE session_id = %s AND provider_type = %s",
            (content, session_id, provider_type)
        )
        
        # 如果没有更新任何行，则插入新记录
        if updated == 0:
            self._execute_sql(
                "INSERT INTO llm_history(provider_type, session_id, content) VALUES (%s, %s, %s)",
                (provider_type, session_id, content)
            )
    
    def get_llm_history(self, session_id: str = None, provider_type: str = None) -> List[LLMHistory]:
        """获取LLM历史记录"""
        conditions = []
        params = []
        
        if session_id:
            conditions.append("session_id = %s")
            params.append(session_id)
        
        if provider_type:
            conditions.append("provider_type = %s")
            params.append(provider_type)
        
        sql_query = "SELECT * FROM llm_history"
        if conditions:
            sql_query += " WHERE " + " AND ".join(conditions)
        
        rows = self._execute_sql(sql_query, params, fetch=True)
        
        histories = []
        for row in rows:
            # 适配 LLMHistory 对象的构造
            histories.append(LLMHistory(
                row['id'], row['provider_type'], row['session_id'], row['content']
            ))
        
        return histories

    def get_base_stats(self, offset_sec: int = 86400) -> Stats:
        """获取基础统计数据"""
        timestamp_threshold = int(time.time()) - offset_sec

        # 获取平台统计
        platform_rows = self._execute_sql(
            "SELECT * FROM platform WHERE timestamp >= %s",
            (timestamp_threshold,),
            fetch=True
        )

        platform = []
        for row in platform_rows:
            platform.append(Platform(row['name'], row['count'], row['timestamp']))

        return Stats(platform, [], [])

    def get_total_message_count(self) -> int:
        """获取总消息数量"""
        rows = self._execute_sql("SELECT SUM(count) as total FROM platform", fetch=True)
        return rows[0]['total'] if rows and rows[0]['total'] else 0

    def get_grouped_base_stats(self, offset_sec: int = 86400) -> Stats:
        """获取分组的基础统计数据"""
        timestamp_threshold = int(time.time()) - offset_sec

        platform_rows = self._execute_sql(
            "SELECT name, SUM(count) as count, MAX(timestamp) as timestamp FROM platform WHERE timestamp >= %s GROUP BY name",
            (timestamp_threshold,),
            fetch=True
        )

        platform = []
        for row in platform_rows:
            platform.append(Platform(row['name'], row['count'], row['timestamp']))

        return Stats(platform, [], [])

    def get_conversation_by_user_id(self, user_id: str, cid: str) -> Optional[Conversation]:
        """根据用户ID和对话ID获取对话"""
        rows = self._execute_sql(
            "SELECT * FROM webchat_conversation WHERE user_id = %s AND cid = %s",
            (user_id, cid),
            fetch=True
        )

        if not rows:
            return None

        row = rows[0]
        return Conversation(
            row['user_id'], row['cid'], row['history'],
            row['created_at'], row['updated_at'],
            row['title'], row['persona_id']
        )

    def new_conversation(self, user_id: str, cid: str):
        """创建新对话"""
        history = "[]"
        timestamp = int(time.time())

        self._execute_sql(
            "INSERT INTO webchat_conversation(user_id, cid, history, updated_at, created_at) VALUES (%s, %s, %s, %s, %s)",
            (user_id, cid, history, timestamp, timestamp)
        )

    def get_conversations(self, user_id: str) -> List[Conversation]:
        """获取用户的所有对话"""
        rows = self._execute_sql(
            "SELECT cid, created_at, updated_at, title, persona_id FROM webchat_conversation WHERE user_id = %s ORDER BY updated_at DESC",
            (user_id,),
            fetch=True
        )

        conversations = []
        for row in rows:
            conversations.append(Conversation(
                "", row['cid'], "[]", row['created_at'], row['updated_at'],
                row['title'], row['persona_id']
            ))

        return conversations

    def update_conversation(self, user_id: str, cid: str, history: str):
        """更新对话历史"""
        updated_at = int(time.time())
        self._execute_sql(
            "UPDATE webchat_conversation SET history = %s, updated_at = %s WHERE user_id = %s AND cid = %s",
            (history, updated_at, user_id, cid)
        )

    def update_conversation_title(self, user_id: str, cid: str, title: str):
        """更新对话标题"""
        self._execute_sql(
            "UPDATE webchat_conversation SET title = %s WHERE user_id = %s AND cid = %s",
            (title, user_id, cid)
        )

    def update_conversation_persona_id(self, user_id: str, cid: str, persona_id: str):
        """更新对话人格ID"""
        self._execute_sql(
            "UPDATE webchat_conversation SET persona_id = %s WHERE user_id = %s AND cid = %s",
            (persona_id, user_id, cid)
        )

    def delete_conversation(self, user_id: str, cid: str):
        """删除对话"""
        self._execute_sql(
            "DELETE FROM webchat_conversation WHERE user_id = %s AND cid = %s",
            (user_id, cid)
        )

    def insert_atri_vision_data(self, vision: ATRIVision):
        """插入ATRI视觉数据"""
        ts = int(time.time())
        keywords = ",".join(vision.keywords) if vision.keywords else ""

        self._execute_sql(
            """INSERT INTO atri_vision(id, url_or_path, caption, is_meme, keywords,
               platform_name, session_id, sender_nickname, timestamp)
               VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
            (vision.id, vision.url_or_path, vision.caption, vision.is_meme,
             keywords, vision.platform_name, vision.session_id,
             vision.sender_nickname, ts)
        )

    def get_atri_vision_data(self) -> List[ATRIVision]:
        """获取所有ATRI视觉数据"""
        rows = self._execute_sql("SELECT * FROM atri_vision", fetch=True)

        visions = []
        for row in rows:
            keywords = row['keywords'].split(',') if row['keywords'] else []
            visions.append(ATRIVision(
                row['id'], row['url_or_path'], row['caption'], row['is_meme'],
                keywords, row['platform_name'], row['session_id'],
                row['sender_nickname'], row['timestamp']
            ))

        return visions

    def get_atri_vision_data_by_path_or_id(self, url_or_path: str, id: str) -> Optional[ATRIVision]:
        """根据路径或ID获取ATRI视觉数据"""
        rows = self._execute_sql(
            "SELECT * FROM atri_vision WHERE url_or_path = %s OR id = %s",
            (url_or_path, id),
            fetch=True
        )

        if not rows:
            return None

        row = rows[0]
        keywords = row['keywords'].split(',') if row['keywords'] else []
        return ATRIVision(
            row['id'], row['url_or_path'], row['caption'], row['is_meme'],
            keywords, row['platform_name'], row['session_id'],
            row['sender_nickname'], row['timestamp']
        )

    def get_all_conversations(self, page: int = 1, page_size: int = 20) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有对话，支持分页"""
        try:
            # 获取总数
            count_rows = self._execute_sql("SELECT COUNT(*) as total FROM webchat_conversation", fetch=True)
            total_count = count_rows[0]['total'] if count_rows else 0

            # 计算偏移量
            offset = (page - 1) * page_size

            # 获取分页数据
            rows = self._execute_sql(
                """SELECT user_id, cid, created_at, updated_at, title, persona_id
                   FROM webchat_conversation
                   ORDER BY updated_at DESC
                   LIMIT %s OFFSET %s""",
                (page_size, offset),
                fetch=True
            )

            conversations = []
            for row in rows:
                safe_cid = str(row['cid']) if row['cid'] else "unknown"
                display_cid = safe_cid[:8] if len(safe_cid) >= 8 else safe_cid

                conversations.append({
                    "user_id": row['user_id'] or "",
                    "cid": safe_cid,
                    "title": row['title'] or f"对话 {display_cid}",
                    "persona_id": row['persona_id'] or "",
                    "created_at": row['created_at'] or 0,
                    "updated_at": row['updated_at'] or 0,
                })

            return conversations, total_count

        except Exception as e:
            logger.error(f"获取所有对话失败: {e}")
            return [], 0

    def get_filtered_conversations(
        self,
        page: int = 1,
        page_size: int = 20,
        platforms: List[str] = None,
        message_types: List[str] = None,
        search_query: str = None,
        exclude_ids: List[str] = None,
        exclude_platforms: List[str] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取筛选后的对话列表"""
        try:
            # 构建查询条件
            where_clauses = []
            params = []

            # 平台筛选
            if platforms and len(platforms) > 0:
                platform_conditions = []
                for platform in platforms:
                    platform_conditions.append("user_id LIKE %s")
                    params.append(f"{platform}:%")

                if platform_conditions:
                    where_clauses.append(f"({' OR '.join(platform_conditions)})")

            # 消息类型筛选
            if message_types and len(message_types) > 0:
                message_type_conditions = []
                for msg_type in message_types:
                    message_type_conditions.append("user_id LIKE %s")
                    params.append(f"%:{msg_type}:%")

                if message_type_conditions:
                    where_clauses.append(f"({' OR '.join(message_type_conditions)})")

            # 搜索关键词
            if search_query:
                where_clauses.append(
                    "(title ILIKE %s OR user_id ILIKE %s OR cid ILIKE %s OR history ILIKE %s)"
                )
                search_param = f"%{search_query}%"
                params.extend([search_param, search_param, search_param, search_param])

            # 排除特定用户ID
            if exclude_ids and len(exclude_ids) > 0:
                for exclude_id in exclude_ids:
                    where_clauses.append("user_id NOT LIKE %s")
                    params.append(f"{exclude_id}%")

            # 排除特定平台
            if exclude_platforms and len(exclude_platforms) > 0:
                for exclude_platform in exclude_platforms:
                    where_clauses.append("user_id NOT LIKE %s")
                    params.append(f"{exclude_platform}:%")

            # 构建完整的 WHERE 子句
            where_sql = " WHERE " + " AND ".join(where_clauses) if where_clauses else ""

            # 获取总记录数
            count_sql = f"SELECT COUNT(*) as total FROM webchat_conversation{where_sql}"
            count_rows = self._execute_sql(count_sql, params, fetch=True)
            total_count = count_rows[0]['total'] if count_rows else 0

            # 计算偏移量
            offset = (page - 1) * page_size

            # 构建分页数据查询
            data_sql = f"""
                SELECT user_id, cid, created_at, updated_at, title, persona_id
                FROM webchat_conversation
                {where_sql}
                ORDER BY updated_at DESC
                LIMIT %s OFFSET %s
            """
            query_params = params + [page_size, offset]

            # 获取分页数据
            rows = self._execute_sql(data_sql, query_params, fetch=True)

            conversations = []
            for row in rows:
                safe_cid = str(row['cid']) if row['cid'] else "unknown"
                display_cid = safe_cid[:8] if len(safe_cid) >= 8 else safe_cid

                conversations.append({
                    "user_id": row['user_id'] or "",
                    "cid": safe_cid,
                    "title": row['title'] or f"对话 {display_cid}",
                    "persona_id": row['persona_id'] or "",
                    "created_at": row['created_at'] or 0,
                    "updated_at": row['updated_at'] or 0,
                })

            return conversations, total_count

        except Exception as e:
            logger.error(f"获取筛选对话失败: {e}")
            return [], 0

    def get_all_conversations(self, page: int = 1, page_size: int = 20) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有对话，支持分页"""
        try:
            # 获取总数
            count_rows = self._execute_sql("SELECT COUNT(*) as total FROM webchat_conversation", fetch=True)
            total_count = count_rows[0]['total'] if count_rows else 0

            # 计算偏移量
            offset = (page - 1) * page_size

            # 获取分页数据
            rows = self._execute_sql(
                """SELECT user_id, cid, created_at, updated_at, title, persona_id
                   FROM webchat_conversation
                   ORDER BY updated_at DESC
                   LIMIT %s OFFSET %s""",
                (page_size, offset),
                fetch=True
            )

            conversations = []
            for row in rows:
                safe_cid = str(row['cid']) if row['cid'] else "unknown"
                display_cid = safe_cid[:8] if len(safe_cid) >= 8 else safe_cid

                conversations.append({
                    "user_id": row['user_id'] or "",
                    "cid": safe_cid,
                    "title": row['title'] or f"对话 {display_cid}",
                    "persona_id": row['persona_id'] or "",
                    "created_at": row['created_at'] or 0,
                    "updated_at": row['updated_at'] or 0,
                })

            return conversations, total_count

        except Exception as e:
            logger.error(f"获取所有对话失败: {e}")
            return [], 0

    def insert_atri_vision_data(self, vision: ATRIVision):
        """插入ATRI视觉数据"""
        ts = int(time.time())
        keywords = ",".join(vision.keywords) if vision.keywords else ""

        self._execute_sql(
            """INSERT INTO atri_vision(id, url_or_path, caption, is_meme, keywords,
               platform_name, session_id, sender_nickname, timestamp)
               VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
            (vision.id, vision.url_or_path, vision.caption, vision.is_meme,
             keywords, vision.platform_name, vision.session_id,
             vision.sender_nickname, ts)
        )

    def insert_atri_vision_data(self, vision: ATRIVision):
        """插入ATRI视觉数据"""
        ts = int(time.time())
        keywords = ",".join(vision.keywords) if vision.keywords else ""

        self._execute_sql(
            """INSERT INTO atri_vision(id, url_or_path, caption, is_meme, keywords,
               platform_name, session_id, sender_nickname, timestamp)
               VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
            (vision.id, vision.url_or_path, vision.caption, vision.is_meme,
             keywords, vision.platform_name, vision.session_id,
             vision.sender_nickname, ts)
        )

    def get_atri_vision_data(self) -> List[ATRIVision]:
        """获取所有ATRI视觉数据"""
        rows = self._execute_sql("SELECT * FROM atri_vision", fetch=True)

        visions = []
        for row in rows:
            keywords = row['keywords'].split(',') if row['keywords'] else []
            visions.append(ATRIVision(
                row['id'], row['url_or_path'], row['caption'], row['is_meme'],
                keywords, row['platform_name'], row['session_id'],
                row['sender_nickname'], row['timestamp']
            ))

        return visions

    def get_atri_vision_data_by_path_or_id(self, url_or_path: str, id: str) -> Optional[ATRIVision]:
        """根据路径或ID获取ATRI视觉数据"""
        rows = self._execute_sql(
            "SELECT * FROM atri_vision WHERE url_or_path = %s OR id = %s",
            (url_or_path, id),
            fetch=True
        )

        if not rows:
            return None

        row = rows[0]
        keywords = row['keywords'].split(',') if row['keywords'] else []
        return ATRIVision(
            row['id'], row['url_or_path'], row['caption'], row['is_meme'],
            keywords, row['platform_name'], row['session_id'],
            row['sender_nickname'], row['timestamp']
        )

    def insert_atri_vision_data(self, vision: ATRIVision):
        """插入ATRI视觉数据"""
        ts = int(time.time())
        keywords = ",".join(vision.keywords) if vision.keywords else ""

        self._execute_sql(
            """INSERT INTO atri_vision(id, url_or_path, caption, is_meme, keywords,
               platform_name, session_id, sender_nickname, timestamp)
               VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)""",
            (vision.id, vision.url_or_path, vision.caption, vision.is_meme,
             keywords, vision.platform_name, vision.session_id,
             vision.sender_nickname, ts)
        )

    def get_atri_vision_data(self) -> List[ATRIVision]:
        """获取所有ATRI视觉数据"""
        rows = self._execute_sql("SELECT * FROM atri_vision", fetch=True)

        visions = []
        for row in rows:
            keywords = row['keywords'].split(',') if row['keywords'] else []
            visions.append(ATRIVision(
                row['id'], row['url_or_path'], row['caption'], row['is_meme'],
                keywords, row['platform_name'], row['session_id'],
                row['sender_nickname'], row['timestamp']
            ))

        return visions

    def get_atri_vision_data_by_path_or_id(self, url_or_path: str, id: str) -> Optional[ATRIVision]:
        """根据路径或ID获取ATRI视觉数据"""
        rows = self._execute_sql(
            "SELECT * FROM atri_vision WHERE url_or_path = %s OR id = %s",
            (url_or_path, id),
            fetch=True
        )

        if not rows:
            return None

        row = rows[0]
        keywords = row['keywords'].split(',') if row['keywords'] else []
        return ATRIVision(
            row['id'], row['url_or_path'], row['caption'], row['is_meme'],
            keywords, row['platform_name'], row['session_id'],
            row['sender_nickname'], row['timestamp']
        )

    def get_all_conversations(self, page: int = 1, page_size: int = 20) -> Tuple[List[Dict[str, Any]], int]:
        """获取所有对话，支持分页"""
        # 获取总数
        count_rows = self._execute_sql("SELECT COUNT(*) as total FROM webchat_conversation", fetch=True)
        total_count = count_rows[0]['total'] if count_rows else 0

        # 计算偏移量
        offset = (page - 1) * page_size

        # 获取分页数据
        rows = self._execute_sql(
            """SELECT user_id, cid, created_at, updated_at, title, persona_id
               FROM webchat_conversation
               ORDER BY updated_at DESC
               LIMIT %s OFFSET %s""",
            (page_size, offset),
            fetch=True
        )

        conversations = []
        for row in rows:
            safe_cid = str(row['cid']) if row['cid'] else "unknown"
            display_cid = safe_cid[:8] if len(safe_cid) >= 8 else safe_cid

            conversations.append({
                "user_id": row['user_id'] or "",
                "cid": safe_cid,
                "title": row['title'] or f"对话 {display_cid}",
                "persona_id": row['persona_id'] or "",
                "created_at": row['created_at'] or 0,
                "updated_at": row['updated_at'] or 0,
            })

        return conversations, total_count
