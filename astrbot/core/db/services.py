"""
数据库 Service 层
处理业务逻辑和数据库操作
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy.sql import func

from .models import MCPServer, MCPMarket, User
from .schemas import (
    MCPServerCreate, MCPServerUpdate, MCPServerResponse,
    MCPMarketCreate, MCPMarketUpdate, MCPMarketResponse,
    UserCreate, UserUpdate, UserResponse
)
from astrbot.core import logger


class MCPServerService:
    """MCP Server 服务层"""
    
    @staticmethod
    def get_server(db: Session, name: str, user_id: str = None) -> Optional[MCPServerResponse]:
        """根据名称获取服务器（可选用户维度）"""
        query = db.query(MCPServer).filter(MCPServer.name == name)
        if user_id:
            query = query.filter(MCPServer.user_id == user_id)

        server = query.first()
        if server:
            return MCPServerResponse.from_orm(server)
        return None
    
    @staticmethod
    def get_servers(db: Session, skip: int = 0, limit: int = 100, user_id: str = None) -> List[MCPServerResponse]:
        """获取服务器列表（可选用户维度）"""
        query = db.query(MCPServer)
        if user_id:
            query = query.filter(MCPServer.user_id == user_id)

        servers = query.offset(skip).limit(limit).all()
        return [MCPServerResponse.from_orm(server) for server in servers]
    
    @staticmethod
    def get_all_servers(db: Session, user_id: str = None) -> List[MCPServerResponse]:
        """获取所有服务器（可选用户维度）"""
        query = db.query(MCPServer)
        if user_id:
            query = query.filter(MCPServer.user_id == user_id)

        servers = query.all()
        return [MCPServerResponse.from_orm(server) for server in servers]
    
    @staticmethod
    def create_server(db: Session, server_data: MCPServerCreate) -> MCPServerResponse:
        """创建服务器"""
        try:
            # 检查服务器是否已存在
            existing = db.query(MCPServer).filter(MCPServer.name == server_data.name).first()
            if existing:
                raise ValueError(f"服务器 {server_data.name} 已存在")
            
            # 创建新服务器
            db_server = MCPServer(
                name=server_data.name,
                active=server_data.active,
                config=server_data.config
            )
            db.add(db_server)
            db.commit()
            db.refresh(db_server)
            
            logger.info(f"创建 MCP 服务器: {server_data.name}")
            return MCPServerResponse.from_orm(db_server)
            
        except IntegrityError:
            db.rollback()
            raise ValueError(f"服务器 {server_data.name} 已存在")
        except Exception as e:
            db.rollback()
            logger.error(f"创建 MCP 服务器失败: {e}")
            raise
    
    @staticmethod
    def update_server(db: Session, name: str, server_data: MCPServerUpdate) -> Optional[MCPServerResponse]:
        """更新服务器"""
        try:
            server = db.query(MCPServer).filter(MCPServer.name == name).first()
            if not server:
                return None
            
            # 更新字段
            update_data = server_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(server, field, value)
            
            db.commit()
            db.refresh(server)
            
            logger.info(f"更新 MCP 服务器: {name}")
            return MCPServerResponse.from_orm(server)
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新 MCP 服务器失败: {e}")
            raise
    
    @staticmethod
    def delete_server(db: Session, name: str) -> bool:
        """删除服务器"""
        try:
            server = db.query(MCPServer).filter(MCPServer.name == name).first()
            if not server:
                return False
            
            db.delete(server)
            db.commit()
            
            logger.info(f"删除 MCP 服务器: {name}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"删除 MCP 服务器失败: {e}")
            raise
    
    @staticmethod
    def get_active_servers(db: Session) -> List[MCPServerResponse]:
        """获取活跃的服务器"""
        servers = db.query(MCPServer).filter(MCPServer.active == True).all()
        return [MCPServerResponse.from_orm(server) for server in servers]
    
    @staticmethod
    def count_servers(db: Session) -> int:
        """获取服务器总数"""
        return db.query(MCPServer).count()
    
    @staticmethod
    def server_exists(db: Session, name: str) -> bool:
        """检查服务器是否存在"""
        return db.query(MCPServer).filter(MCPServer.name == name).first() is not None


class MCPMarketService:
    """MCP Market 服务层"""
    
    @staticmethod
    def get_market_item(db: Session, name: str) -> Optional[MCPMarketResponse]:
        """根据名称获取市场条目"""
        item = db.query(MCPMarket).filter(MCPMarket.name == name).first()
        if item:
            return MCPMarketResponse.from_orm(item)
        return None
    
    @staticmethod
    def get_market_items(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        pinned_first: bool = True
    ) -> List[MCPMarketResponse]:
        """获取市场条目列表"""
        query = db.query(MCPMarket)
        
        if pinned_first:
            query = query.order_by(
                MCPMarket.pinned.desc(),
                MCPMarket.stars.desc(),
                MCPMarket.updated_at.desc()
            )
        else:
            query = query.order_by(MCPMarket.updated_at.desc())
        
        items = query.offset(skip).limit(limit).all()
        return [MCPMarketResponse.from_orm(item) for item in items]
    
    @staticmethod
    def get_all_market_items(db: Session) -> List[MCPMarketResponse]:
        """获取所有市场条目"""
        items = db.query(MCPMarket).order_by(
            MCPMarket.pinned.desc(),
            MCPMarket.stars.desc(),
            MCPMarket.updated_at.desc()
        ).all()
        return [MCPMarketResponse.from_orm(item) for item in items]
    
    @staticmethod
    def create_market_item(db: Session, item_data: MCPMarketCreate) -> MCPMarketResponse:
        """创建市场条目"""
        try:
            # 检查条目是否已存在
            existing = db.query(MCPMarket).filter(MCPMarket.name == item_data.name).first()
            if existing:
                raise ValueError(f"市场条目 {item_data.name} 已存在")
            
            # 创建新条目
            db_item = MCPMarket(**item_data.dict())
            db.add(db_item)
            db.commit()
            db.refresh(db_item)
            
            logger.info(f"创建 MCP 市场条目: {item_data.name}")
            return MCPMarketResponse.from_orm(db_item)
            
        except IntegrityError:
            db.rollback()
            raise ValueError(f"市场条目 {item_data.name} 已存在")
        except Exception as e:
            db.rollback()
            logger.error(f"创建 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def update_market_item(db: Session, name: str, item_data: MCPMarketUpdate) -> Optional[MCPMarketResponse]:
        """更新市场条目"""
        try:
            item = db.query(MCPMarket).filter(MCPMarket.name == name).first()
            if not item:
                return None
            
            # 更新字段
            update_data = item_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(item, field, value)
            
            db.commit()
            db.refresh(item)
            
            logger.info(f"更新 MCP 市场条目: {name}")
            return MCPMarketResponse.from_orm(item)
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def delete_market_item(db: Session, name: str) -> bool:
        """删除市场条目"""
        try:
            item = db.query(MCPMarket).filter(MCPMarket.name == name).first()
            if not item:
                return False
            
            db.delete(item)
            db.commit()
            
            logger.info(f"删除 MCP 市场条目: {name}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"删除 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def clear_all_market_items(db: Session) -> int:
        """清空所有市场条目"""
        try:
            count = db.query(MCPMarket).count()
            db.query(MCPMarket).delete()
            db.commit()
            
            logger.info(f"清空 MCP 市场条目: {count} 个")
            return count
            
        except Exception as e:
            db.rollback()
            logger.error(f"清空 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def bulk_create_market_items(db: Session, items_data: List[MCPMarketCreate]) -> int:
        """批量创建市场条目"""
        try:
            created_count = 0
            
            for item_data in items_data:
                # 使用 merge 来处理重复项
                db_item = MCPMarket(**item_data.dict())
                db.merge(db_item)
                created_count += 1
            
            db.commit()
            logger.info(f"批量创建 MCP 市场条目: {created_count} 个")
            return created_count
            
        except Exception as e:
            db.rollback()
            logger.error(f"批量创建 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def count_market_items(db: Session) -> int:
        """获取市场条目总数"""
        return db.query(MCPMarket).count()
    
    @staticmethod
    def search_market_items(db: Session, keyword: str) -> List[MCPMarketResponse]:
        """搜索市场条目"""
        items = db.query(MCPMarket).filter(
            MCPMarket.name.ilike(f"%{keyword}%") |
            MCPMarket.description.ilike(f"%{keyword}%") |
            MCPMarket.author.ilike(f"%{keyword}%")
        ).order_by(
            MCPMarket.pinned.desc(),
            MCPMarket.stars.desc()
        ).all()
        
        return [MCPMarketResponse.from_orm(item) for item in items]


class UserService:
    """用户服务层"""

    @staticmethod
    def get_user(db: Session, user_id: str) -> Optional[UserResponse]:
        """根据ID获取用户"""
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            return UserResponse.from_orm(user)
        return None

    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[UserResponse]:
        """根据用户名获取用户"""
        user = db.query(User).filter(User.username == username).first()
        if user:
            return UserResponse.from_orm(user)
        return None

    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[UserResponse]:
        """根据邮箱获取用户"""
        user = db.query(User).filter(User.email == email).first()
        if user:
            return UserResponse.from_orm(user)
        return None

    @staticmethod
    def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[UserResponse]:
        """获取用户列表"""
        users = db.query(User).offset(skip).limit(limit).all()
        return [UserResponse.from_orm(user) for user in users]

    @staticmethod
    def create_user(db: Session, user_data: UserCreate) -> UserResponse:
        """创建用户"""
        try:
            # 检查用户是否已存在
            existing = db.query(User).filter(User.id == user_data.id).first()
            if existing:
                raise ValueError(f"用户 {user_data.id} 已存在")

            # 检查用户名是否已存在
            existing_username = db.query(User).filter(User.username == user_data.username).first()
            if existing_username:
                raise ValueError(f"用户名 {user_data.username} 已存在")

            # 创建新用户
            db_user = User(
                id=user_data.id,
                username=user_data.username,
                email=user_data.email,
                name=user_data.name,
                is_active=user_data.is_active,
                is_admin=user_data.is_admin,
                extra_info=user_data.extra_info
            )
            db.add(db_user)
            db.commit()
            db.refresh(db_user)

            logger.info(f"创建用户: {user_data.username} (ID: {user_data.id})")
            return UserResponse.from_orm(db_user)

        except IntegrityError:
            db.rollback()
            raise ValueError(f"用户 {user_data.id} 已存在")
        except Exception as e:
            db.rollback()
            logger.error(f"创建用户失败: {e}")
            raise

    @staticmethod
    def update_user(db: Session, user_id: str, user_data: UserUpdate) -> Optional[UserResponse]:
        """更新用户"""
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return None

            # 更新字段
            update_data = user_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(user, field, value)

            db.commit()
            db.refresh(user)

            logger.info(f"更新用户: {user.username} (ID: {user_id})")
            return UserResponse.from_orm(user)

        except Exception as e:
            db.rollback()
            logger.error(f"更新用户失败: {e}")
            raise

    @staticmethod
    def delete_user(db: Session, user_id: str) -> bool:
        """删除用户"""
        try:
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                return False

            db.delete(user)
            db.commit()

            logger.info(f"删除用户: {user.username} (ID: {user_id})")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"删除用户失败: {e}")
            raise

    @staticmethod
    def user_exists(db: Session, user_id: str) -> bool:
        """检查用户是否存在"""
        return db.query(User).filter(User.id == user_id).first() is not None

    @staticmethod
    def create_or_update_user_from_oauth(db: Session, oauth_user_info: dict) -> UserResponse:
        """从OAuth信息创建或更新用户"""
        try:
            user_id = oauth_user_info.get('id')
            username = oauth_user_info.get('username', '')
            email = oauth_user_info.get('email')
            name = oauth_user_info.get('name')

            if not user_id:
                raise ValueError("OAuth用户信息缺少用户ID")

            # 检查用户是否已存在
            existing_user = db.query(User).filter(User.id == user_id).first()

            if existing_user:
                # 更新现有用户信息
                existing_user.username = username or existing_user.username
                existing_user.email = email or existing_user.email
                existing_user.name = name or existing_user.name
                existing_user.last_login_at = func.now()

                db.commit()
                db.refresh(existing_user)

                logger.info(f"更新OAuth用户: {existing_user.username} (ID: {user_id})")
                return UserResponse.from_orm(existing_user)
            else:
                # 创建新用户
                new_user = User(
                    id=user_id,
                    username=username or f"user_{user_id[:8]}",
                    email=email,
                    name=name,
                    is_active=True,
                    is_admin=False,
                    last_login_at=func.now(),
                    extra_info=oauth_user_info
                )
                db.add(new_user)
                db.commit()
                db.refresh(new_user)

                logger.info(f"创建OAuth用户: {new_user.username} (ID: {user_id})")
                return UserResponse.from_orm(new_user)

        except Exception as e:
            db.rollback()
            logger.error(f"创建或更新OAuth用户失败: {e}")
            raise
