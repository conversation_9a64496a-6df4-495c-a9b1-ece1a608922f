"""
数据库 Service 层
处理业务逻辑和数据库操作
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from .models import MCPServer, MCPMarket
from .schemas import (
    MCPServerCreate, MCPServerUpdate, MCPServerResponse,
    MCPMarketCreate, MCPMarketUpdate, MCPMarketResponse
)
from astrbot.core import logger


class MCPServerService:
    """MCP Server 服务层"""
    
    @staticmethod
    def get_server(db: Session, name: str) -> Optional[MCPServerResponse]:
        """根据名称获取服务器"""
        server = db.query(MCPServer).filter(MCPServer.name == name).first()
        if server:
            return MCPServerResponse.from_orm(server)
        return None
    
    @staticmethod
    def get_servers(db: Session, skip: int = 0, limit: int = 100) -> List[MCPServerResponse]:
        """获取服务器列表"""
        servers = db.query(MCPServer).offset(skip).limit(limit).all()
        return [MCPServerResponse.from_orm(server) for server in servers]
    
    @staticmethod
    def get_all_servers(db: Session) -> List[MCPServerResponse]:
        """获取所有服务器"""
        servers = db.query(MCPServer).all()
        return [MCPServerResponse.from_orm(server) for server in servers]
    
    @staticmethod
    def create_server(db: Session, server_data: MCPServerCreate) -> MCPServerResponse:
        """创建服务器"""
        try:
            # 检查服务器是否已存在
            existing = db.query(MCPServer).filter(MCPServer.name == server_data.name).first()
            if existing:
                raise ValueError(f"服务器 {server_data.name} 已存在")
            
            # 创建新服务器
            db_server = MCPServer(
                name=server_data.name,
                active=server_data.active,
                config=server_data.config
            )
            db.add(db_server)
            db.commit()
            db.refresh(db_server)
            
            logger.info(f"创建 MCP 服务器: {server_data.name}")
            return MCPServerResponse.from_orm(db_server)
            
        except IntegrityError:
            db.rollback()
            raise ValueError(f"服务器 {server_data.name} 已存在")
        except Exception as e:
            db.rollback()
            logger.error(f"创建 MCP 服务器失败: {e}")
            raise
    
    @staticmethod
    def update_server(db: Session, name: str, server_data: MCPServerUpdate) -> Optional[MCPServerResponse]:
        """更新服务器"""
        try:
            server = db.query(MCPServer).filter(MCPServer.name == name).first()
            if not server:
                return None
            
            # 更新字段
            update_data = server_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(server, field, value)
            
            db.commit()
            db.refresh(server)
            
            logger.info(f"更新 MCP 服务器: {name}")
            return MCPServerResponse.from_orm(server)
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新 MCP 服务器失败: {e}")
            raise
    
    @staticmethod
    def delete_server(db: Session, name: str) -> bool:
        """删除服务器"""
        try:
            server = db.query(MCPServer).filter(MCPServer.name == name).first()
            if not server:
                return False
            
            db.delete(server)
            db.commit()
            
            logger.info(f"删除 MCP 服务器: {name}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"删除 MCP 服务器失败: {e}")
            raise
    
    @staticmethod
    def get_active_servers(db: Session) -> List[MCPServerResponse]:
        """获取活跃的服务器"""
        servers = db.query(MCPServer).filter(MCPServer.active == True).all()
        return [MCPServerResponse.from_orm(server) for server in servers]
    
    @staticmethod
    def count_servers(db: Session) -> int:
        """获取服务器总数"""
        return db.query(MCPServer).count()
    
    @staticmethod
    def server_exists(db: Session, name: str) -> bool:
        """检查服务器是否存在"""
        return db.query(MCPServer).filter(MCPServer.name == name).first() is not None


class MCPMarketService:
    """MCP Market 服务层"""
    
    @staticmethod
    def get_market_item(db: Session, name: str) -> Optional[MCPMarketResponse]:
        """根据名称获取市场条目"""
        item = db.query(MCPMarket).filter(MCPMarket.name == name).first()
        if item:
            return MCPMarketResponse.from_orm(item)
        return None
    
    @staticmethod
    def get_market_items(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        pinned_first: bool = True
    ) -> List[MCPMarketResponse]:
        """获取市场条目列表"""
        query = db.query(MCPMarket)
        
        if pinned_first:
            query = query.order_by(
                MCPMarket.pinned.desc(),
                MCPMarket.stars.desc(),
                MCPMarket.updated_at.desc()
            )
        else:
            query = query.order_by(MCPMarket.updated_at.desc())
        
        items = query.offset(skip).limit(limit).all()
        return [MCPMarketResponse.from_orm(item) for item in items]
    
    @staticmethod
    def get_all_market_items(db: Session) -> List[MCPMarketResponse]:
        """获取所有市场条目"""
        items = db.query(MCPMarket).order_by(
            MCPMarket.pinned.desc(),
            MCPMarket.stars.desc(),
            MCPMarket.updated_at.desc()
        ).all()
        return [MCPMarketResponse.from_orm(item) for item in items]
    
    @staticmethod
    def create_market_item(db: Session, item_data: MCPMarketCreate) -> MCPMarketResponse:
        """创建市场条目"""
        try:
            # 检查条目是否已存在
            existing = db.query(MCPMarket).filter(MCPMarket.name == item_data.name).first()
            if existing:
                raise ValueError(f"市场条目 {item_data.name} 已存在")
            
            # 创建新条目
            db_item = MCPMarket(**item_data.dict())
            db.add(db_item)
            db.commit()
            db.refresh(db_item)
            
            logger.info(f"创建 MCP 市场条目: {item_data.name}")
            return MCPMarketResponse.from_orm(db_item)
            
        except IntegrityError:
            db.rollback()
            raise ValueError(f"市场条目 {item_data.name} 已存在")
        except Exception as e:
            db.rollback()
            logger.error(f"创建 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def update_market_item(db: Session, name: str, item_data: MCPMarketUpdate) -> Optional[MCPMarketResponse]:
        """更新市场条目"""
        try:
            item = db.query(MCPMarket).filter(MCPMarket.name == name).first()
            if not item:
                return None
            
            # 更新字段
            update_data = item_data.dict(exclude_unset=True)
            for field, value in update_data.items():
                setattr(item, field, value)
            
            db.commit()
            db.refresh(item)
            
            logger.info(f"更新 MCP 市场条目: {name}")
            return MCPMarketResponse.from_orm(item)
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def delete_market_item(db: Session, name: str) -> bool:
        """删除市场条目"""
        try:
            item = db.query(MCPMarket).filter(MCPMarket.name == name).first()
            if not item:
                return False
            
            db.delete(item)
            db.commit()
            
            logger.info(f"删除 MCP 市场条目: {name}")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"删除 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def clear_all_market_items(db: Session) -> int:
        """清空所有市场条目"""
        try:
            count = db.query(MCPMarket).count()
            db.query(MCPMarket).delete()
            db.commit()
            
            logger.info(f"清空 MCP 市场条目: {count} 个")
            return count
            
        except Exception as e:
            db.rollback()
            logger.error(f"清空 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def bulk_create_market_items(db: Session, items_data: List[MCPMarketCreate]) -> int:
        """批量创建市场条目"""
        try:
            created_count = 0
            
            for item_data in items_data:
                # 使用 merge 来处理重复项
                db_item = MCPMarket(**item_data.dict())
                db.merge(db_item)
                created_count += 1
            
            db.commit()
            logger.info(f"批量创建 MCP 市场条目: {created_count} 个")
            return created_count
            
        except Exception as e:
            db.rollback()
            logger.error(f"批量创建 MCP 市场条目失败: {e}")
            raise
    
    @staticmethod
    def count_market_items(db: Session) -> int:
        """获取市场条目总数"""
        return db.query(MCPMarket).count()
    
    @staticmethod
    def search_market_items(db: Session, keyword: str) -> List[MCPMarketResponse]:
        """搜索市场条目"""
        items = db.query(MCPMarket).filter(
            MCPMarket.name.ilike(f"%{keyword}%") |
            MCPMarket.description.ilike(f"%{keyword}%") |
            MCPMarket.author.ilike(f"%{keyword}%")
        ).order_by(
            MCPMarket.pinned.desc(),
            MCPMarket.stars.desc()
        ).all()
        
        return [MCPMarketResponse.from_orm(item) for item in items]
