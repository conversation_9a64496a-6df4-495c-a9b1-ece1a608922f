from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, declarative_base

try:
    from astrbot.core.config.env_config import env_config
except Exception as e:
    raise RuntimeError("Failed to import env_config for database configuration") from e


# 仅支持 PostgreSQL
DATABASE_URL = env_config.DATABASE_URL

# SQLAlchemy Engine / Session
engine = create_engine(
    DATABASE_URL,
    pool_pre_ping=True,
    future=True,
)
SessionLocal = sessionmaker(
    autocommit=False,
    autoflush=False,
    bind=engine,
    expire_on_commit=False,
    future=True,
)

Base = declarative_base()


def init_db():
    # 延迟导入 models，避免循环导入
    from . import models  # noqa: F401
    Base.metadata.create_all(bind=engine)


