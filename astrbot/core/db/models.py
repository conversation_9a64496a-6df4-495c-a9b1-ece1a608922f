from sqlalchemy import Column, String, Integer, Text, BigInteger, Boolean, DateTime
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func

from .orm import Base


class MCPMarket(Base):
    __tablename__ = "mcp_market"

    # 主键：市场条目名称（唯一标识）
    name = Column(String(255), primary_key=True, index=True)

    description = Column(Text, nullable=True)
    author = Column(String(255), nullable=True)
    repo = Column(Text, nullable=True)
    version = Column(String(64), nullable=True)
    social_link = Column(Text, nullable=True)

    # 使用 JSONB 存储标签（仅 PostgreSQL）
    tags = Column(JSONB, nullable=True)

    logo = Column(Text, nullable=True)
    stars = Column(Integer, nullable=False, default=0)
    updated_at = Column(BigInteger, nullable=False, default=0)
    pinned = Column(Boolean, nullable=False, default=False)

    created_at = Column(DateTime, server_default=func.now())


class MCPServer(Base):
    __tablename__ = "mcp_server"

    name = Column(String(255), primary_key=True, index=True)
    active = Column(Boolean, nullable=False, default=True)
    # 完整配置 JSON（包含 url/headers/transport 或 stdio 的 command/args/cwd/env 等）
    config = Column(JSONB, nullable=False, default={})
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())


