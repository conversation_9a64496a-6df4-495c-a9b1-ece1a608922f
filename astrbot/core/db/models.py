from sqlalchemy import Column, String, Integer, Text, BigInteger, Boolean, DateTime, ForeignKey
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from .orm import Base


class User(Base):
    __tablename__ = "users"

    # OAuth用户ID作为主键（来自OAuth服务器的用户ID）
    id = Column(String(255), primary_key=True, index=True)

    # 用户基本信息（来自OAuth）
    username = Column(String(255), nullable=False, index=True)
    email = Column(String(255), nullable=True, index=True)
    name = Column(String(255), nullable=True)

    # 用户状态和权限
    is_active = Column(Boolean, nullable=False, default=True)
    is_admin = Column(Boolean, nullable=False, default=False)

    # 时间戳
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())
    last_login_at = Column(DateTime, nullable=True)

    # 额外的用户信息（JSON格式存储其他OAuth字段）
    extra_info = Column(JSONB, nullable=True, default={})

    # 关系：一个用户可以有多个MCP Server
    mcp_servers = relationship("MCPServer", back_populates="user", cascade="all, delete-orphan")


class MCPMarket(Base):
    __tablename__ = "mcp_market"

    # 主键：市场条目名称（唯一标识）
    name = Column(String(255), primary_key=True, index=True)

    description = Column(Text, nullable=True)
    author = Column(String(255), nullable=True)
    repo = Column(Text, nullable=True)
    version = Column(String(64), nullable=True)
    social_link = Column(Text, nullable=True)

    # 使用 JSONB 存储标签（仅 PostgreSQL）
    tags = Column(JSONB, nullable=True)

    logo = Column(Text, nullable=True)
    stars = Column(Integer, nullable=False, default=0)
    updated_at = Column(BigInteger, nullable=False, default=0)
    pinned = Column(Boolean, nullable=False, default=False)

    created_at = Column(DateTime, server_default=func.now())


class MCPServer(Base):
    __tablename__ = "mcp_server"

    name = Column(String(255), primary_key=True, index=True)
    active = Column(Boolean, nullable=False, default=True)
    # 完整配置 JSON（包含 url/headers/transport 或 stdio 的 command/args/cwd/env 等）
    config = Column(JSONB, nullable=False, default={})
    created_at = Column(DateTime, server_default=func.now())
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now())

    # 用户维度：关联到用户
    user_id = Column(String(255), ForeignKey("users.id"), nullable=False, index=True)

    # 关系：每个MCP Server属于一个用户
    user = relationship("User", back_populates="mcp_servers")


