import logging
from typing import Union
from .postgresql import PostgreSQLDatabase
from .base import BaseDatabase

logger = logging.getLogger("astrbot")


class DatabaseFactory:
    """数据库工厂类，根据配置创建相应的数据库实例"""
    
    @staticmethod
    def create_database(database_url: str) -> BaseDatabase:
        """根据数据库URL创建数据库实例（仅支持 PostgreSQL）"""
        if database_url.startswith('postgresql'):
            logger.info(f"使用 PostgreSQL 数据库: {database_url}")
            return PostgreSQLDatabase(database_url)
        raise ValueError(f"不支持的数据库URL格式: {database_url}（当前项目仅支持 PostgreSQL）")
    
    @staticmethod
    def create_database_from_config(config) -> BaseDatabase:
        """从配置对象创建数据库实例
        
        Args:
            config: 配置对象，需要有 database_type 和 database_url 属性
        
        Returns:
            BaseDatabase: 数据库实例
        """
        # 从环境配置获取数据库信息（必须为 PostgreSQL）
        from astrbot.core.config.env_config import env_config
        database_url = env_config.DATABASE_URL
        logger.info(f"从环境配置获取数据库URL: {database_url}")
        return DatabaseFactory.create_database(database_url)


# 全局数据库实例
_database_instance = None


def get_database() -> BaseDatabase:
    """获取全局数据库实例（单例模式）"""
    global _database_instance
    
    if _database_instance is None:
        _database_instance = DatabaseFactory.create_database_from_config(None)
    
    return _database_instance


def reset_database():
    """重置数据库实例（主要用于测试）"""
    global _database_instance
    _database_instance = None
