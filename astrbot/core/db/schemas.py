"""
数据库 Schema 层
定义 Pydantic 模型用于数据验证和序列化
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field, validator, EmailStr


# ==================== User Schemas ====================

class UserBase(BaseModel):
    """用户基础 Schema"""
    id: str = Field(..., max_length=255, description="OAuth用户ID")
    username: str = Field(..., max_length=255, description="用户名")
    email: Optional[str] = Field(None, description="邮箱")
    name: Optional[str] = Field(None, max_length=255, description="显示名称")
    is_active: bool = Field(default=True, description="是否激活")
    is_admin: bool = Field(default=False, description="是否管理员")
    extra_info: Optional[Dict[str, Any]] = Field(default_factory=dict, description="额外信息")

    @validator('id')
    def validate_id(cls, v):
        if not v or not v.strip():
            raise ValueError('用户ID不能为空')
        return v.strip()

    @validator('username')
    def validate_username(cls, v):
        if not v or not v.strip():
            raise ValueError('用户名不能为空')
        return v.strip()

    @validator('email')
    def validate_email(cls, v):
        if v and not v.strip():
            return None
        return v


class UserCreate(UserBase):
    """创建用户的 Schema"""
    pass


class UserUpdate(BaseModel):
    """更新用户的 Schema"""
    username: Optional[str] = Field(None, max_length=255, description="用户名")
    email: Optional[str] = Field(None, description="邮箱")
    name: Optional[str] = Field(None, max_length=255, description="显示名称")
    is_active: Optional[bool] = Field(None, description="是否激活")
    is_admin: Optional[bool] = Field(None, description="是否管理员")
    extra_info: Optional[Dict[str, Any]] = Field(None, description="额外信息")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")

    @validator('username')
    def validate_username(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('用户名不能为空')
        return v.strip() if v else v

    @validator('email')
    def validate_email(cls, v):
        if v and not v.strip():
            return None
        return v


class UserResponse(UserBase):
    """用户响应 Schema"""
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    last_login_at: Optional[datetime] = Field(None, description="最后登录时间")

    class Config:
        from_attributes = True


# ==================== MCP Schemas ====================

class MCPMarketBase(BaseModel):
    """MCP Market 基础 Schema"""
    name: str = Field(..., max_length=255, description="市场条目名称")
    description: Optional[str] = Field(None, description="描述")
    author: Optional[str] = Field(None, max_length=255, description="作者")
    repo: Optional[str] = Field(None, description="仓库地址")
    version: Optional[str] = Field(None, max_length=64, description="版本")
    social_link: Optional[str] = Field(None, description="社交链接")
    tags: Optional[List[str]] = Field(default_factory=list, description="标签列表")
    logo: Optional[str] = Field(None, description="Logo URL")
    stars: int = Field(default=0, ge=0, description="星标数")
    updated_at: int = Field(default=0, ge=0, description="更新时间戳")
    pinned: bool = Field(default=False, description="是否置顶")


class MCPMarketCreate(MCPMarketBase):
    """创建 MCP Market 的 Schema"""
    pass


class MCPMarketUpdate(BaseModel):
    """更新 MCP Market 的 Schema"""
    description: Optional[str] = Field(None, description="描述")
    author: Optional[str] = Field(None, max_length=255, description="作者")
    repo: Optional[str] = Field(None, description="仓库地址")
    version: Optional[str] = Field(None, max_length=64, description="版本")
    social_link: Optional[str] = Field(None, description="社交链接")
    tags: Optional[List[str]] = Field(None, description="标签列表")
    logo: Optional[str] = Field(None, description="Logo URL")
    stars: Optional[int] = Field(None, ge=0, description="星标数")
    updated_at: Optional[int] = Field(None, ge=0, description="更新时间戳")
    pinned: Optional[bool] = Field(None, description="是否置顶")


class MCPMarketResponse(MCPMarketBase):
    """MCP Market 响应 Schema"""
    created_at: Optional[datetime] = Field(None, description="创建时间")

    class Config:
        from_attributes = True


class MCPServerBase(BaseModel):
    """MCP Server 基础 Schema"""
    name: str = Field(..., max_length=255, description="服务器名称")
    active: bool = Field(default=True, description="是否激活")
    config: Dict[str, Any] = Field(default_factory=dict, description="服务器配置")
    user_id: str = Field(..., max_length=255, description="所属用户ID")

    @validator('name')
    def validate_name(cls, v):
        if not v or not v.strip():
            raise ValueError('服务器名称不能为空')
        return v.strip()

    @validator('config')
    def validate_config(cls, v):
        if not isinstance(v, dict):
            raise ValueError('配置必须是字典类型')
        return v

    @validator('user_id')
    def validate_user_id(cls, v):
        if not v or not v.strip():
            raise ValueError('用户ID不能为空')
        return v.strip()


class MCPServerCreate(MCPServerBase):
    """创建 MCP Server 的 Schema"""
    pass


class MCPServerUpdate(BaseModel):
    """更新 MCP Server 的 Schema"""
    active: Optional[bool] = Field(None, description="是否激活")
    config: Optional[Dict[str, Any]] = Field(None, description="服务器配置")
    user_id: Optional[str] = Field(None, max_length=255, description="所属用户ID")

    @validator('config')
    def validate_config(cls, v):
        if v is not None and not isinstance(v, dict):
            raise ValueError('配置必须是字典类型')
        return v

    @validator('user_id')
    def validate_user_id(cls, v):
        if v is not None and (not v or not v.strip()):
            raise ValueError('用户ID不能为空')
        return v.strip() if v else v


class MCPServerResponse(MCPServerBase):
    """MCP Server 响应 Schema"""
    created_at: Optional[datetime] = Field(None, description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    
    # 运行时信息（不存储在数据库中）
    tools: Optional[List[str]] = Field(default_factory=list, description="工具列表")
    errlogs: Optional[List[str]] = Field(default_factory=list, description="错误日志")

    class Config:
        from_attributes = True


class MCPServerListResponse(BaseModel):
    """MCP Server 列表响应 Schema"""
    servers: List[MCPServerResponse]
    total: int = Field(..., description="总数量")


class MCPMarketListResponse(BaseModel):
    """MCP Market 列表响应 Schema"""
    items: List[MCPMarketResponse]
    total: int = Field(..., description="总数量")


# 用于 API 请求的 Schema
class MCPServerAddRequest(BaseModel):
    """添加 MCP Server 请求 Schema"""
    name: str = Field(..., description="服务器名称")
    active: bool = Field(default=True, description="是否激活")
    
    # 支持多种配置格式
    command: Optional[str] = Field(None, description="命令")
    args: Optional[List[str]] = Field(None, description="参数列表")
    cwd: Optional[str] = Field(None, description="工作目录")
    env: Optional[Dict[str, str]] = Field(None, description="环境变量")
    
    # HTTP 配置
    url: Optional[str] = Field(None, description="HTTP URL")
    headers: Optional[Dict[str, str]] = Field(None, description="HTTP 头")
    
    # 嵌套的 mcpServers 格式（兼容旧格式）
    mcpServers: Optional[Dict[str, Dict[str, Any]]] = Field(None, description="嵌套服务器配置")

    def to_server_config(self) -> Dict[str, Any]:
        """转换为服务器配置字典"""
        config = {"active": self.active}
        
        # 处理嵌套格式
        if self.mcpServers:
            key_0 = list(self.mcpServers.keys())[0]
            config.update(self.mcpServers[key_0])
            return config
        
        # 处理直接字段
        for field in ["command", "args", "cwd", "env", "url", "headers"]:
            value = getattr(self, field, None)
            if value is not None:
                config[field] = value
        
        return config


class MCPServerUpdateRequest(BaseModel):
    """更新 MCP Server 请求 Schema"""
    name: str = Field(..., description="服务器名称")
    active: Optional[bool] = Field(None, description="是否激活")
    
    # 支持多种配置格式（同 AddRequest）
    command: Optional[str] = Field(None, description="命令")
    args: Optional[List[str]] = Field(None, description="参数列表")
    cwd: Optional[str] = Field(None, description="工作目录")
    env: Optional[Dict[str, str]] = Field(None, description="环境变量")
    url: Optional[str] = Field(None, description="HTTP URL")
    headers: Optional[Dict[str, str]] = Field(None, description="HTTP 头")
    mcpServers: Optional[Dict[str, Dict[str, Any]]] = Field(None, description="嵌套服务器配置")

    def to_server_config(self) -> Dict[str, Any]:
        """转换为服务器配置字典"""
        config = {}
        
        if self.active is not None:
            config["active"] = self.active
        
        # 处理嵌套格式
        if self.mcpServers:
            key_0 = list(self.mcpServers.keys())[0]
            config.update(self.mcpServers[key_0])
            return config
        
        # 处理直接字段
        for field in ["command", "args", "cwd", "env", "url", "headers"]:
            value = getattr(self, field, None)
            if value is not None:
                config[field] = value
        
        return config


class MCPServerDeleteRequest(BaseModel):
    """删除 MCP Server 请求 Schema"""
    name: str = Field(..., description="服务器名称")


class MCPServerTestRequest(BaseModel):
    """测试 MCP Server 连接请求 Schema"""
    mcp_server_config: Dict[str, Any] = Field(..., description="服务器配置")


class MCPMarketInitRequest(BaseModel):
    """初始化 MCP Market 请求 Schema"""
    page: int = Field(default=1, ge=1, description="页码")
    page_size: int = Field(default=200, ge=1, le=1000, description="每页大小")
