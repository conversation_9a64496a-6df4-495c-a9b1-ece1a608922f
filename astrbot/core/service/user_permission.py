from ryjx.iam.permission_client import permission_client
from astrbot.core import logger
from astrbot.core.utils.cache_utils import cache_result


class FakeRequest:
    headers = {}
    def __init__(self, headers):
        self.headers = headers


def _permission_cache_key(*args, **kwargs):
    """生成权限缓存键

    兼容实例方法被装饰时自动注入的 self 参数：
    - 对于实例方法，args 形如 (self, token, user_id)
    - 对于普通函数，args 形如 (token, user_id)
    """
    token = None
    user_id = None
    # 尝试从位置参数解析
    if len(args) >= 3:
        # (self, token, user_id)
        token = args[1]
        user_id = args[2]
    elif len(args) == 2:
        # (token, user_id)
        token, user_id = args
    else:
        # 回退从关键字参数解析
        token = kwargs.get("token")
        user_id = kwargs.get("user_id")

    return f"user_{user_id}"


class UserPermissionService:
    @cache_result(prefix="user_permission", ttl=300, key_func=_permission_cache_key)
    async def is_admin(self, token, user_id):
        """检查用户是否为管理员（带缓存）

        Args:
            token: 用户访问令牌
            user_id: 用户ID

        Returns:
            bool: True表示是管理员，False表示不是
        """
        try:
            logger.debug(f"调用权限API检查用户权限: {user_id}")
            request = FakeRequest(headers={"Authorization": f"Bearer {token}"})
            has_write_permission = await permission_client.check_write_permission(
                request=request,
                user_id=user_id,
                object_code="all.all"
            )

            logger.debug(f"权限检查完成: {user_id} -> {has_write_permission}")
            return has_write_permission

        except Exception as e:
            # 记录错误但不抛出异常，返回 False 表示没有权限
            logger.error(f"权限检查失败 - 用户ID: {user_id}, 错误: {e}")
            return False