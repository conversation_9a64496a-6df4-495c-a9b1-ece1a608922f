# Redis 缓存系统配置指南

## 概述

AstrBot 现已集成 Redis 缓存系统，用于提高性能和减少重复的 API 调用。系统支持 Redis 和内存缓存两种模式，可根据环境自动选择。

## 功能特性

### ✅ 缓存功能
- **用户信息缓存** - 缓存 OAuth 用户信息，减少重复的 API 调用
- **灵活的 TTL** - 支持自定义缓存过期时间
- **自动降级** - Redis 不可用时自动切换到内存缓存
- **批量操作** - 支持按前缀批量清理缓存

### ✅ 双模式支持
- **Redis 模式** - 生产环境推荐，支持分布式缓存
- **内存模式** - 开发环境或 Redis 不可用时的备选方案

## 环境变量配置

### Redis 配置项

在 `.env.{environment}` 文件中添加以下配置：

```bash
# Redis 缓存配置
REDIS_ENABLED=true                    # 是否启用 Redis 缓存
REDIS_HOST=localhost                  # Redis 服务器主机
REDIS_PORT=6379                       # Redis 服务器端口
REDIS_PASSWORD=                       # Redis 密码（可选）
REDIS_DB=0                           # Redis 数据库编号
REDIS_TTL=3600                       # 默认缓存过期时间（秒）
```

### 配置说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `REDIS_ENABLED` | `false` | 是否启用 Redis 缓存 |
| `REDIS_HOST` | `localhost` | Redis 服务器地址 |
| `REDIS_PORT` | `6379` | Redis 服务器端口 |
| `REDIS_PASSWORD` | `None` | Redis 认证密码 |
| `REDIS_DB` | `0` | Redis 数据库编号 |
| `REDIS_TTL` | `3600` | 默认缓存过期时间（1小时） |

## 安装 Redis

### Docker 方式（推荐）

```bash
# 启动 Redis 容器
docker run -d \
  --name astrbot-redis \
  -p 6379:6379 \
  redis:7-alpine

# 带密码的 Redis
docker run -d \
  --name astrbot-redis \
  -p 6379:6379 \
  redis:7-alpine \
  redis-server --requirepass your_password
```

### 系统安装

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**CentOS/RHEL:**
```bash
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis
```

**macOS:**
```bash
brew install redis
brew services start redis
```

## Python 依赖

确保安装了 Redis Python 客户端：

```bash
pip install redis
# 或者异步版本
pip install redis[hiredis]
```

## 使用示例

### 基本缓存操作

```python
from astrbot.core.utils.cache_utils import cache_manager

# 初始化缓存管理器
await cache_manager.initialize()

# 设置缓存
await cache_manager.set("user", "user_123", {"name": "张三"}, ttl=1800)

# 获取缓存
user_data = await cache_manager.get("user", "user_123")

# 检查缓存是否存在
exists = await cache_manager.exists("user", "user_123")

# 删除缓存
await cache_manager.delete("user", "user_123")

# 清除指定前缀的所有缓存
await cache_manager.clear_prefix("user")
```

### 用户信息缓存

```python
from astrbot.core.utils.cache_utils import (
    get_cached_user_info,
    set_cached_user_info,
    delete_cached_user_info
)

# 获取缓存的用户信息
user_info = await get_cached_user_info(access_token)

# 设置用户信息缓存
await set_cached_user_info(access_token, {
    "id": "123",
    "username": "user123",
    "email": "<EMAIL>"
})

# 删除用户信息缓存
await delete_cached_user_info(access_token)
```

### 缓存装饰器

```python
from astrbot.core.utils.cache_utils import cache_result

@cache_result("api_data", ttl=600)  # 缓存 10 分钟
async def fetch_expensive_data(param1, param2):
    # 耗时的 API 调用或计算
    return await some_expensive_operation(param1, param2)

# 第一次调用会执行函数并缓存结果
result1 = await fetch_expensive_data("a", "b")

# 第二次调用会直接从缓存返回
result2 = await fetch_expensive_data("a", "b")  # 从缓存获取
```

## 缓存键命名规范

系统使用以下命名规范：

```
astrbot:{prefix}:{hash_id}
```

- `astrbot` - 应用前缀
- `{prefix}` - 功能前缀（如 `user_info`、`api_data`）
- `{hash_id}` - 标识符的 MD5 哈希值

### 常用前缀

| 前缀 | 用途 | 示例键 |
|------|------|--------|
| `user_info` | 用户信息缓存 | `astrbot:user_info:abc123...` |
| `api_data` | API 数据缓存 | `astrbot:api_data:def456...` |
| `session` | 会话数据缓存 | `astrbot:session:ghi789...` |

## 监控和调试

### 查看缓存状态

```python
# 检查 Redis 连接状态
from astrbot.core.utils.cache_utils import cache_manager

await cache_manager.initialize()
if cache_manager._redis_client:
    print("使用 Redis 缓存")
    # 获取 Redis 信息
    info = await cache_manager._redis_client.info()
    print(f"Redis 版本: {info['redis_version']}")
    print(f"已用内存: {info['used_memory_human']}")
else:
    print("使用内存缓存")
```

### Redis CLI 命令

```bash
# 连接到 Redis
redis-cli

# 查看所有 AstrBot 相关的键
KEYS astrbot:*

# 查看特定键的值
GET astrbot:user_info:abc123...

# 查看键的过期时间
TTL astrbot:user_info:abc123...

# 删除所有 AstrBot 缓存
EVAL "return redis.call('del', unpack(redis.call('keys', 'astrbot:*')))" 0
```

## 性能优化

### 缓存策略建议

1. **用户信息** - TTL 设置为 1-2 小时
2. **API 数据** - 根据数据更新频率设置，通常 5-30 分钟
3. **会话数据** - 设置为用户会话时长，通常 24 小时

### 内存使用优化

```bash
# Redis 配置优化
maxmemory 256mb
maxmemory-policy allkeys-lru
```

## 测试验证

运行测试脚本验证缓存系统：

```bash
cd astrbot
python test_cache_system.py
```

测试内容包括：
- ✅ 配置加载验证
- ✅ 缓存管理器基本功能
- ✅ 用户信息缓存功能
- ✅ 错误处理和降级机制

## 故障排除

### 常见问题

1. **Redis 连接失败**
   ```
   Redis 连接失败，将使用内存缓存: [Errno 111] Connection refused
   ```
   - 检查 Redis 服务是否启动
   - 验证主机和端口配置
   - 检查防火墙设置

2. **认证失败**
   ```
   Redis 连接失败: NOAUTH Authentication required
   ```
   - 检查 `REDIS_PASSWORD` 配置
   - 确认 Redis 服务器密码设置

3. **内存不足**
   ```
   OOM command not allowed when used memory > 'maxmemory'
   ```
   - 增加 Redis 内存限制
   - 调整内存淘汰策略
   - 清理过期缓存

### 日志查看

```bash
# 查看 AstrBot 日志中的缓存相关信息
grep -i "cache\|redis" data/logs/astrbot.log

# 查看 Redis 日志
tail -f /var/log/redis/redis-server.log
```

## 生产环境建议

1. **高可用部署** - 使用 Redis Sentinel 或 Cluster
2. **监控告警** - 监控 Redis 内存使用和连接数
3. **备份策略** - 定期备份 Redis 数据（如果包含重要数据）
4. **安全配置** - 设置强密码，限制访问 IP

## 总结

Redis 缓存系统为 AstrBot 提供了：
- ✅ 显著的性能提升
- ✅ 减少外部 API 调用
- ✅ 更好的用户体验
- ✅ 灵活的配置选项
- ✅ 自动降级机制

通过合理配置和使用缓存系统，可以大幅提升 AstrBot 的响应速度和稳定性。
