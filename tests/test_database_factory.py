#!/usr/bin/env python3
"""
数据库工厂测试
"""

import pytest
import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from astrbot.core.db.database_factory import DatabaseFactory
from astrbot.core.db.sqlite import SQLiteDatabase
from astrbot.core.db.postgresql import PostgreSQLDatabase


class TestDatabaseFactory:
    """数据库工厂测试类"""
    
    def test_create_sqlite_database(self):
        """测试创建 SQLite 数据库"""
        # 测试文件数据库
        db = DatabaseFactory.create_database("sqlite:///test.db")
        assert isinstance(db, SQLiteDatabase)
        
        # 测试内存数据库
        db = DatabaseFactory.create_database("sqlite:///:memory:")
        assert isinstance(db, SQLiteDatabase)
    
    def test_create_postgresql_database(self):
        """测试创建 PostgreSQL 数据库"""
        # 注意：这个测试需要有效的 PostgreSQL 连接才能通过
        # 在 CI/CD 环境中可能需要跳过
        postgresql_url = "postgresql://test:test@localhost:5432/test"
        
        try:
            db = DatabaseFactory.create_database(postgresql_url)
            assert isinstance(db, PostgreSQLDatabase)
        except Exception as e:
            # 如果 PostgreSQL 不可用，跳过测试
            pytest.skip(f"PostgreSQL 不可用: {e}")
    
    def test_invalid_database_url(self):
        """测试无效的数据库 URL"""
        # 无效的 URL 应该回退到 SQLite
        db = DatabaseFactory.create_database("invalid://url")
        assert isinstance(db, SQLiteDatabase)
    
    def test_create_database_from_config(self):
        """测试从配置创建数据库"""
        # 设置测试环境变量
        original_env = os.environ.get('APP_ENV')
        os.environ['APP_ENV'] = 'local'

        try:
            db = DatabaseFactory.create_database_from_config(None)
            assert db is not None
        finally:
            # 恢复原始环境变量
            if original_env:
                os.environ['APP_ENV'] = original_env
            elif 'APP_ENV' in os.environ:
                del os.environ['APP_ENV']


if __name__ == '__main__':
    # 运行测试
    pytest.main([__file__, '-v'])
