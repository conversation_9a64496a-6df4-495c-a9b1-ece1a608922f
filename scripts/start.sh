#!/bin/bash

# AstrBot 启动脚本
# 支持多环境配置

set -e

# 默认环境
DEFAULT_ENV="local"

# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "AstrBot 启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -e, --env ENV        设置运行环境 (local, dev, beta, prod)"
    echo "  -h, --help          显示此帮助信息"
    echo "  --migrate           执行数据库迁移"
    echo "  --check-config      检查配置文件"
    echo ""
    echo "示例:"
    echo "  $0                   # 使用默认环境 (local)"
    echo "  $0 -e dev            # 使用开发环境"
    echo "  $0 -e prod           # 使用正式环境"
    echo "  $0 --migrate         # 执行数据库迁移"
    echo ""
}

# 检查配置文件
check_config() {
    local env=$1
    local config_file="$PROJECT_ROOT/config/.env.$env"
    
    if [[ ! -f "$config_file" ]]; then
        print_warning "环境配置文件不存在: $config_file"
        print_info "正在从示例文件创建..."
        
        if [[ -f "$PROJECT_ROOT/config/.env.example" ]]; then
            cp "$PROJECT_ROOT/config/.env.example" "$config_file"
            print_success "已创建配置文件: $config_file"
            print_warning "请编辑配置文件并设置正确的参数"
        else
            print_error "示例配置文件不存在: $PROJECT_ROOT/config/.env.example"
            return 1
        fi
    else
        print_success "找到配置文件: $config_file"
    fi
}

# 执行数据库迁移
run_migration() {
    print_info "执行数据库迁移..."
    cd "$PROJECT_ROOT"
    python scripts/migrate_database.py --dry-run
    
    read -p "是否继续执行迁移? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        python scripts/migrate_database.py
        print_success "数据库迁移完成"
    else
        print_info "取消迁移"
    fi
}

# 启动应用
start_app() {
    local env=$1
    
    print_info "启动 AstrBot (环境: $env)"
    
    # 设置环境变量
    export APP_ENV="$env"
    
    # 切换到项目根目录
    cd "$PROJECT_ROOT"
    
    # 启动应用
    python main.py
}

# 解析命令行参数
ENV="$DEFAULT_ENV"
MIGRATE=false
CHECK_CONFIG_ONLY=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -e|--env)
            ENV="$2"
            shift 2
            ;;
        -h|--help)
            show_help
            exit 0
            ;;
        --migrate)
            MIGRATE=true
            shift
            ;;
        --check-config)
            CHECK_CONFIG_ONLY=true
            shift
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证环境参数
if [[ ! "$ENV" =~ ^(local|dev|beta|prod)$ ]]; then
    print_error "无效的环境: $ENV"
    print_info "支持的环境: local, dev, beta, prod"
    exit 1
fi

print_info "使用环境: $ENV"

# 检查配置文件
check_config "$ENV"

# 如果只是检查配置，则退出
if [[ "$CHECK_CONFIG_ONLY" == true ]]; then
    exit 0
fi

# 如果需要迁移数据库
if [[ "$MIGRATE" == true ]]; then
    run_migration
    exit 0
fi

# 启动应用
start_app "$ENV"
