#!/usr/bin/env python3
"""
数据库迁移脚本
从 SQLite 迁移数据到 PostgreSQL
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from astrbot.core.db.sqlite import SQLiteDatabase
from astrbot.core.db.postgresql import PostgreSQLDatabase
from astrbot.core.config.env_config import env_config

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def migrate_data(sqlite_path: str, postgresql_url: str):
    """迁移数据从 SQLite 到 PostgreSQL"""
    
    logger.info(f"开始数据迁移: {sqlite_path} -> {postgresql_url}")
    
    # 创建数据库连接
    sqlite_db = SQLiteDatabase(sqlite_path)
    postgresql_db = PostgreSQLDatabase(postgresql_url)
    
    try:
        # 迁移平台统计数据
        logger.info("迁移平台统计数据...")
        platform_stats = sqlite_db.get_base_stats(offset_sec=365*24*3600)  # 获取一年的数据
        for platform in platform_stats.platform:
            postgresql_db.insert_platform_metrics({platform.name: platform.count})
        
        # 迁移 LLM 历史记录
        logger.info("迁移 LLM 历史记录...")
        llm_histories = sqlite_db.get_llm_history()
        for history in llm_histories:
            postgresql_db.update_llm_history(
                history.session_id, 
                history.content, 
                history.provider_type
            )
        
        # 迁移对话数据
        logger.info("迁移对话数据...")
        conversations, total = sqlite_db.get_all_conversations(page=1, page_size=10000)
        for conv in conversations:
            # 获取完整的对话数据
            full_conv = sqlite_db.get_conversation_by_user_id(conv['user_id'], conv['cid'])
            if full_conv:
                # 创建新对话
                postgresql_db.new_conversation(full_conv.user_id, full_conv.cid)
                # 更新对话内容
                postgresql_db.update_conversation(
                    full_conv.user_id, 
                    full_conv.cid, 
                    full_conv.history
                )
                # 更新标题和人格ID
                if full_conv.title:
                    postgresql_db.update_conversation_title(
                        full_conv.user_id, 
                        full_conv.cid, 
                        full_conv.title
                    )
                if full_conv.persona_id:
                    postgresql_db.update_conversation_persona_id(
                        full_conv.user_id, 
                        full_conv.cid, 
                        full_conv.persona_id
                    )
        
        # 迁移 ATRI 视觉数据
        logger.info("迁移 ATRI 视觉数据...")
        vision_data = sqlite_db.get_atri_vision_data()
        for vision in vision_data:
            postgresql_db.insert_atri_vision_data(vision)
        
        logger.info("数据迁移完成！")
        
    except Exception as e:
        logger.error(f"数据迁移失败: {e}")
        raise


def main():
    parser = argparse.ArgumentParser(description='数据库迁移工具')
    parser.add_argument('--sqlite-path', 
                       help='SQLite 数据库文件路径',
                       default='data/data_v3.db')
    parser.add_argument('--postgresql-url',
                       help='PostgreSQL 连接URL',
                       default=None)
    parser.add_argument('--dry-run',
                       action='store_true',
                       help='仅检查连接，不执行迁移')
    
    args = parser.parse_args()
    
    # 获取 PostgreSQL URL
    postgresql_url = args.postgresql_url
    if not postgresql_url:
        if env_config.database_type == 'postgresql':
            postgresql_url = env_config.database_url
        else:
            logger.error("请提供 PostgreSQL 连接URL 或在环境配置中设置")
            return 1
    
    # 检查 SQLite 文件是否存在
    if not os.path.exists(args.sqlite_path):
        logger.error(f"SQLite 数据库文件不存在: {args.sqlite_path}")
        return 1
    
    if args.dry_run:
        logger.info("执行连接测试...")
        try:
            sqlite_db = SQLiteDatabase(args.sqlite_path)
            postgresql_db = PostgreSQLDatabase(postgresql_url)
            logger.info("连接测试成功！")
            return 0
        except Exception as e:
            logger.error(f"连接测试失败: {e}")
            return 1
    
    try:
        migrate_data(args.sqlite_path, postgresql_url)
        return 0
    except Exception as e:
        logger.error(f"迁移失败: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
