#!/usr/bin/env python3
"""
初始化 MCP 市场数据到本地数据库（离线脚本）

用法示例：
  python scripts/init_mcp_market.py --page 1 --page-size 500 \
    --remote-url https://api.soulter.top/astrbot/mcpservers

依赖：
  - 使用项目自带数据库工厂从环境变量读取数据库连接（APP_ENV/DATABASE_URL）。
  - 需要 aiohttp（项目已有依赖）。
"""

import asyncio
import argparse
import json
import sys

try:
    import aiohttp
except Exception as e:
    print("[ERROR] 需要安装 aiohttp 依赖：pip install aiohttp", file=sys.stderr)
    raise


def _get_db():
    # 延迟导入，复用项目内数据库工厂
    from astrbot.core.db.database_factory import get_database
    return get_database()


async def fetch_market(remote_url: str, page: int, page_size: int) -> list[dict]:
    url = f"{remote_url}?page={page}&page_size={page_size}"
    async with aiohttp.ClientSession() as session:
        async with session.get(url) as resp:
            if resp.status != 200:
                raise RuntimeError(f"远端请求失败: HTTP {resp.status}")
            payload = await resp.json()
            return payload.get("data", [])


def upsert_items_with_orm(items: list[dict]):
    from astrbot.core.db.orm import SessionLocal, init_db
    from astrbot.core.db.models import MCPMarket
    init_db()
    with SessionLocal() as session:
        session.query(MCPMarket).delete()
        for it in items:
            obj = MCPMarket(
                name=it.get("name"),
                description=it.get("desc"),
                author=it.get("author"),
                repo=it.get("repo"),
                version=it.get("version"),
                social_link=it.get("social_link"),
                tags=it.get("tags", []),
                logo=it.get("logo"),
                stars=int(it.get("stars") or 0),
                updated_at=int(it.get("updated_at") or 0),
                pinned=bool(it.get("pinned")),
            )
            session.merge(obj)
        session.commit()


async def main():
    parser = argparse.ArgumentParser(description="初始化 MCP 市场数据到本地数据库")
    parser.add_argument("--page", type=int, default=1, help="页码")
    parser.add_argument("--page-size", type=int, default=500, help="每页数量")
    parser.add_argument(
        "--remote-url",
        type=str,
        default="https://api.soulter.top/astrbot/mcpservers",
        help="远端市场 API 基础地址",
    )
    args = parser.parse_args()

    print("[INFO] 获取远端市场数据...")
    items = await fetch_market(args.remote_url, args.page, args.page_size)
    print(f"[INFO] 拉取到 {len(items)} 条记录")

    print("[INFO] 连接数据库...")
    print("[INFO] 写入本地数据库（SQLAlchemy ORM）...")
    upsert_items_with_orm(items)
    print("[OK] 初始化完成")


if __name__ == "__main__":
    asyncio.run(main())


