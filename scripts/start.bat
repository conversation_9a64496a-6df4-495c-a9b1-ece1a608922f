@echo off
setlocal enabledelayedexpansion

REM AstrBot 启动脚本 (Windows)
REM 支持多环境配置

set "DEFAULT_ENV=local"
set "ENV=%DEFAULT_ENV%"
set "MIGRATE=false"
set "CHECK_CONFIG_ONLY=false"

REM 获取脚本所在目录
set "SCRIPT_DIR=%~dp0"
set "PROJECT_ROOT=%SCRIPT_DIR%.."

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :args_done
if "%~1"=="-e" (
    set "ENV=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="--env" (
    set "ENV=%~2"
    shift
    shift
    goto :parse_args
)
if "%~1"=="-h" goto :show_help
if "%~1"=="--help" goto :show_help
if "%~1"=="--migrate" (
    set "MIGRATE=true"
    shift
    goto :parse_args
)
if "%~1"=="--check-config" (
    set "CHECK_CONFIG_ONLY=true"
    shift
    goto :parse_args
)
echo [ERROR] 未知选项: %~1
goto :show_help

:args_done

REM 验证环境参数
if not "%ENV%"=="local" if not "%ENV%"=="dev" if not "%ENV%"=="beta" if not "%ENV%"=="prod" (
    echo [ERROR] 无效的环境: %ENV%
    echo [INFO] 支持的环境: local, dev, beta, prod
    exit /b 1
)

echo [INFO] 使用环境: %ENV%

REM 检查配置文件
call :check_config "%ENV%"
if errorlevel 1 exit /b 1

REM 如果只是检查配置，则退出
if "%CHECK_CONFIG_ONLY%"=="true" exit /b 0

REM 如果需要迁移数据库
if "%MIGRATE%"=="true" (
    call :run_migration
    exit /b 0
)

REM 启动应用
call :start_app "%ENV%"
exit /b 0

:show_help
echo AstrBot 启动脚本 (Windows)
echo.
echo 用法: %~nx0 [选项]
echo.
echo 选项:
echo   -e, --env ENV        设置运行环境 (local, dev, beta, prod)
echo   -h, --help          显示此帮助信息
echo   --migrate           执行数据库迁移
echo   --check-config      检查配置文件
echo.
echo 示例:
echo   %~nx0                   # 使用默认环境 (local)
echo   %~nx0 -e dev            # 使用开发环境
echo   %~nx0 -e prod           # 使用正式环境
echo   %~nx0 --migrate         # 执行数据库迁移
echo.
exit /b 0

:check_config
set "config_file=%PROJECT_ROOT%\config\.env.%~1"

if not exist "%config_file%" (
    echo [WARNING] 环境配置文件不存在: %config_file%
    echo [INFO] 正在从示例文件创建...
    
    if exist "%PROJECT_ROOT%\config\.env.example" (
        copy "%PROJECT_ROOT%\config\.env.example" "%config_file%" >nul
        echo [SUCCESS] 已创建配置文件: %config_file%
        echo [WARNING] 请编辑配置文件并设置正确的参数
    ) else (
        echo [ERROR] 示例配置文件不存在: %PROJECT_ROOT%\config\.env.example
        exit /b 1
    )
) else (
    echo [SUCCESS] 找到配置文件: %config_file%
)
exit /b 0

:run_migration
echo [INFO] 执行数据库迁移...
cd /d "%PROJECT_ROOT%"
python scripts\migrate_database.py --dry-run

set /p "reply=是否继续执行迁移? (y/N): "
if /i "%reply%"=="y" (
    python scripts\migrate_database.py
    echo [SUCCESS] 数据库迁移完成
) else (
    echo [INFO] 取消迁移
)
exit /b 0

:start_app
echo [INFO] 启动 AstrBot (环境: %~1)

REM 设置环境变量
set "APP_ENV=%~1"

REM 切换到项目根目录
cd /d "%PROJECT_ROOT%"

REM 启动应用
python main.py
exit /b 0
