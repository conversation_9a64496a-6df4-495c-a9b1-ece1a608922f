{"name": "astrbot-dashboard", "version": "1.0.0", "private": true, "author": "CodedThemes", "scripts": {"dev": "vite --host", "build": "vue-tsc --noEmit && vite build", "build-stage": "vue-tsc --noEmit && vite build --base=/vue/free/stage/", "build-prod": "vue-tsc --noEmit && vite build --base=/vue/free/", "preview": "vite preview --port 5050", "typecheck": "vue-tsc --noEmit", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"@guolao/vue-monaco-editor": "^1.5.4", "@tiptap/starter-kit": "2.1.7", "@tiptap/vue-3": "2.1.7", "apexcharts": "3.42.0", "axios": ">=1.6.2 <1.10.0 || >1.10.0 <2.0.0", "axios-mock-adapter": "^1.22.0", "chance": "1.1.11", "d3": "^7.9.0", "date-fns": "2.30.0", "graphology": "^0.26.0", "graphology-layout-force": "^0.2.4", "highlight.js": "^11.11.1", "js-md5": "^0.8.3", "lodash": "4.17.21", "markdown-it": "^14.1.0", "marked": "^15.0.7", "pinia": "2.1.6", "remixicon": "3.5.0", "sigma": "^3.0.2", "vee-validate": "4.11.3", "vite-plugin-vuetify": "1.0.2", "vue": "3.3.4", "vue-i18n": "^11.1.5", "vue-router": "4.2.4", "vue3-apexcharts": "1.4.4", "vue3-print-nb": "0.1.4", "vuetify": "3.7.11", "yup": "1.2.0"}, "devDependencies": {"@mdi/font": "7.2.96", "@rushstack/eslint-patch": "1.3.3", "@types/chance": "1.1.3", "@types/node": "^20.5.7", "@vitejs/plugin-vue": "4.3.3", "@vue/eslint-config-prettier": "8.0.0", "@vue/eslint-config-typescript": "11.0.3", "@vue/tsconfig": "^0.4.0", "eslint": "8.48.0", "eslint-plugin-vue": "9.17.0", "prettier": "3.0.2", "sass": "1.66.1", "sass-loader": "13.3.2", "typescript": "5.1.6", "vite": "4.4.9", "vue-cli-plugin-vuetify": "2.5.8", "vue-tsc": "1.8.8", "vuetify-loader": "^2.0.0-alpha.9"}}