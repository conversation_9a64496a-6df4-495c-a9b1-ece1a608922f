# SSO 单点登录实现指南

## 概述

本项目已完全移除传统的用户名密码登录方式，改为仅支持 SSO (Single Sign-On) 单点登录。这提供了更安全、更便捷的认证体验。

## 实现特性

### 🔐 完全 SSO 认证
- **移除传统登录**：不再支持用户名密码登录
- **OAuth2 流程**：标准的 OAuth2 授权码流程
- **自动重定向**：旧的登录路径自动重定向到 SSO 登录
- **安全防护**：CSRF 保护和状态验证

### 🎨 用户体验
- **现代化界面**：美观的 SSO 登录页面
- **响应式设计**：支持桌面和移动设备
- **加载状态**：清晰的加载和错误提示
- **自动跳转**：登录成功后自动返回原页面

## 文件结构

```
dashboard/src/
├── views/authentication/auth/
│   ├── SSOLoginPage.vue          # SSO 登录页面
│   ├── OAuthCallback.vue         # OAuth 回调处理页面
│   └── authForms/
│       └── AuthLogin.vue         # 传统登录重定向页面
├── composables/
│   └── useSSOLogin.ts            # SSO 登录逻辑
├── stores/
│   └── auth.ts                   # 认证状态管理（已更新）
├── router/
│   ├── AuthRoutes.ts             # 认证路由（已更新）
│   └── index.ts                  # 路由守卫（已更新）
└── api/
    └── index.ts                  # API 接口（已添加 SSO 相关）
```

## 核心组件

### 1. SSOLoginPage.vue
- **功能**：SSO 登录入口页面
- **特性**：
  - 美观的渐变背景和动画效果
  - 品牌展示和功能介绍
  - 一键 SSO 登录按钮
  - 响应式布局

### 2. OAuthCallback.vue
- **功能**：处理 OAuth 回调
- **流程**：
  1. 接收授权码和状态参数
  2. 验证 CSRF 状态
  3. 交换访问令牌
  4. 保存用户信息
  5. 跳转到目标页面

### 3. useSSOLogin.ts
- **功能**：SSO 登录业务逻辑
- **方法**：
  - `handleOAuthLogin()`: 发起 OAuth 登录
  - `checkLoginStatus()`: 检查登录状态

## API 接口

### SSO 相关接口
```typescript
// 获取 OAuth 配置
export const getOAuthConfig = () => axios.get(`${API_PREFIX}/oauth/config`);

// 生成授权 URL
export const generateAuthUrl = (state: string) => { ... };

// 交换访问令牌
export const exchangeToken = (code: string, state?: string) => 
  axios.post(`${API_PREFIX}/oauth/token`, { code, state });

// 获取当前用户信息
export const getCurrentUserInfo = () => axios.get(`${API_PREFIX}/oauth/me`);

// 获取用户详细信息
export const getUserInfo = () => axios.get(`${API_PREFIX}/oauth/userinfo`);
```

## 认证流程

### 1. 用户访问应用
```
用户访问 → 检查认证状态 → 未登录 → 重定向到 /auth/sso-login
```

### 2. SSO 登录流程
```
点击登录按钮 → 生成 state 参数 → 跳转到授权服务器 → 用户授权 → 
回调到 /auth/oauth/callback → 验证并交换令牌 → 保存用户信息 → 跳转到目标页面
```

### 3. 认证状态管理
```typescript
// 认证存储状态
{
  username: string,
  userInfo: any,
  isSSO: boolean,
  returnUrl: string | null
}
```

## 路由配置

### 认证路由
```typescript
{
  path: '/auth',
  children: [
    {
      name: 'SSOLogin',
      path: '/auth/sso-login',
      component: SSOLoginPage
    },
    {
      name: 'OAuthCallback', 
      path: '/auth/oauth/callback',
      component: OAuthCallback
    },
    {
      path: '/auth/login',
      redirect: '/auth/sso-login'  // 重定向旧路径
    }
  ]
}
```

### 路由守卫
- **公开页面**：`/auth/sso-login`, `/auth/oauth/callback`
- **认证检查**：所有其他页面需要有效的 SSO 令牌
- **自动重定向**：未认证用户自动跳转到 SSO 登录

## 安全特性

### 1. CSRF 保护
- 生成随机 `state` 参数
- 回调时验证 `state` 一致性
- 防止跨站请求伪造攻击

### 2. 令牌管理
- 访问令牌存储在 localStorage
- 定期验证令牌有效性
- 自动清理过期认证信息

### 3. 错误处理
- 完整的错误捕获和显示
- 用户友好的错误提示
- 开发环境错误详情记录

## 配置要求

### 后端 OAuth 配置
后端需要提供以下 API 端点：

```
GET  /api/oauth/config        # 获取 OAuth 配置
POST /api/oauth/token         # 交换访问令牌
GET  /api/oauth/me           # 获取当前用户信息
GET  /api/oauth/userinfo     # 获取用户详细信息
```

### OAuth 配置示例
```json
{
  "client_id": "your-client-id",
  "redirect_uri": "http://localhost:3000/auth/oauth/callback",
  "authorize_url": "https://your-oauth-server/oauth/authorize",
  "response_type": "code",
  "scope": "openid profile email"
}
```

## 部署注意事项

### 1. 环境配置
- 确保 OAuth 回调 URL 正确配置
- 生产环境使用 HTTPS
- 正确设置 CORS 策略

### 2. 域名配置
- OAuth 服务器允许的回调域名
- 前端应用的正确域名配置

### 3. 安全设置
- 使用安全的 OAuth 客户端配置
- 定期轮换客户端密钥
- 监控异常登录行为

## 故障排除

### 常见问题

1. **回调 404 错误**
   - 检查路由配置是否正确
   - 确认 OAuth 回调 URL 设置

2. **状态验证失败**
   - 检查 localStorage 是否被清理
   - 确认 state 参数传递正确

3. **令牌交换失败**
   - 检查后端 OAuth 端点
   - 确认客户端配置正确

4. **用户信息获取失败**
   - 检查令牌是否有效
   - 确认用户信息 API 权限

## 后端实现

### 后端文件结构
```
astrbot/
├── dashboard/
│   ├── server.py                     # 主应用服务器（已更新）
│   └── routes/
│       └── oauth_auth.py             # OAuth 路由处理
├── core/
│   ├── utils/
│   │   └── oauth_client.py           # OAuth 客户端实现
│   └── service/
│       └── user_permission.py       # 用户权限服务（已更新）
└── SSO_CONFIG_EXAMPLE.md            # 配置示例文档
```

### 后端关键修改

1. **认证中间件更新**
   - 添加 OAuth 端点到公开接口列表
   - `/oauth/config` 和 `/oauth/token` 不需要认证

2. **OAuth 路由注册**
   - 注册 OAuth 蓝图到 `/api` 和 `/chat-api/api` 前缀
   - 支持新旧 API 路径

3. **OAuth 客户端集成**
   - 保留 ryjx.iam 包依赖
   - 使用环境变量配置
   - 支持标准 OAuth2 流程和权限检查

4. **权限服务集成**
   - 使用 ryjx.iam.permission_client 进行权限检查
   - 支持完整的权限验证流程

### 环境变量配置

后端需要以下环境变量：
```bash
# OAuth2 配置
OIDC_CLIENT_ID="your-oauth-client-id"
OIDC_CLIENT_SECRET="your-oauth-client-secret"
OIDC_AUTHORIZATION_URL="https://oauth-server/oauth/authorize"
OIDC_TOKEN_URL="https://oauth-server/oauth/token"
OIDC_USERINFO_URL="https://oauth-server/oauth/userinfo"
OIDC_REDIRECT_URL="http://localhost:6185/chat/auth/oauth/callback"

# RYJX IAM 配置
IAM_API_BASE_URL="https://your-iam-server/api"
SERVICE_DISCOVERY_URL="https://your-consul-server:8500"
```

## 总结

本 SSO 实现提供了：
- ✅ 完整的 OAuth2 授权码流程
- ✅ 安全的 CSRF 保护
- ✅ 用户友好的界面设计
- ✅ 完善的错误处理
- ✅ 响应式移动端支持
- ✅ 开发环境错误显示集成
- ✅ 前后端完整集成
- ✅ 简化的部署配置

系统已完全移除传统登录方式，确保所有用户都通过安全的 SSO 方式进行认证。后端支持标准的 OAuth2 流程，可以与大多数 OAuth 服务器集成。
