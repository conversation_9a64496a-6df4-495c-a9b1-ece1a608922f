# Axios API 重构总结

## 重构目标
将分散在各个组件中的 axios 调用统一管理，提高代码的可维护性和一致性。

## 重构内容

### 1. 创建统一的 API 模块
- 文件位置：`src/api/index.ts`
- 统一管理所有后端 API 调用
- 配置统一的请求拦截器（Token 认证）
- 设置统一的 API 前缀：`/chat-api/api`

### 2. API 接口分类
重构后的 API 模块包含以下分类：

#### Dashboard 统计
- `getStat()` - 获取统计数据
- `getVersion()` - 获取版本信息
- `getStartTime()` - 获取启动时间

#### 认证相关
- `login()` - 用户登录
- `editAccount()` - 编辑账户信息

#### 配置管理
- `getConfig()` - 获取配置
- `updateAstrbotConfig()` - 更新配置
- `getPlatformConfig()` - 获取平台配置

#### 插件管理
- `getPlugins()` - 获取插件列表
- `installPlugin()` - 安装插件
- `updatePlugin()` - 更新插件
- `enablePlugin()` / `disablePlugin()` - 启用/禁用插件
- `uninstallPlugin()` - 卸载插件
- `reloadPlugin()` - 重载插件

#### 平台管理
- `addPlatform()` - 添加平台
- `updatePlatform()` - 更新平台
- `deletePlatform()` - 删除平台

#### 服务提供商管理
- `addProvider()` - 添加提供商
- `updateProvider()` - 更新提供商
- `deleteProvider()` - 删除提供商
- `getProviderModels()` - 获取提供商模型列表

#### 会话管理
- `getSessionList()` - 获取会话列表
- `updateSessionStatus()` - 更新会话状态
- `updateSessionPersona()` - 更新会话人格
- `updateSessionProvider()` - 更新会话提供商

#### 对话管理
- `listConversations()` - 获取对话列表
- `deleteConversation()` - 删除对话
- `getConversationDetail()` - 获取对话详情
- `updateConversation()` - 更新对话

#### 聊天功能
- `getChatConversations()` - 获取聊天对话
- `newChatConversation()` - 新建聊天对话
- `uploadChatFile()` / `uploadChatImage()` - 文件上传

#### MCP 工具
- `getMcpServers()` - 获取 MCP 服务器
- `addMcpServer()` - 添加 MCP 服务器
- `updateMcpServer()` - 更新 MCP 服务器
- `deleteMcpServer()` - 删除 MCP 服务器

#### 知识库（Alkaid 插件）
- `getKBCollections()` - 获取知识库集合
- `createKBCollection()` - 创建知识库集合
- `uploadKBFile()` - 上传知识库文件
- `searchKBCollection()` - 搜索知识库

#### 长期记忆（Alkaid 插件）
- `getLtmGraph()` - 获取长期记忆图谱
- `searchLtmGraph()` - 搜索长期记忆
- `getLtmFact()` - 获取记忆事实

### 3. 重构的文件列表
以下文件已完成 axios 调用重构：

#### 布局组件
- `src/layouts/full/vertical-header/VerticalHeader.vue`
- `src/layouts/full/vertical-sidebar/VerticalSidebar.vue`

#### 页面组件
- `src/views/ConfigPage.vue`
- `src/views/Settings.vue`
- `src/views/PlatformPage.vue`
- `src/views/ProviderPage.vue`
- `src/views/ConversationPage.vue`
- `src/views/SessionManagementPage.vue`
- `src/views/ToolUsePage.vue`
- `src/views/ConsolePage.vue`
- `src/views/ChatPage.vue`
- `src/views/AlkaidPage_sigma.vue`
- `src/views/alkaid/KnowledgeBase.vue`
- `src/views/alkaid/LongTermMemory.vue`
- `src/views/dashboards/default/DefaultDashboard.vue`

#### 共享组件
- `src/components/shared/WaitingForRestart.vue`
- `src/components/shared/ReadmeDialog.vue`
- `src/components/shared/ProxySelector.vue`
- `src/components/chat/ProviderModelSelector.vue`

#### Store
- `src/stores/common.js`

### 4. 重构方式
1. **静态导入替换**：将直接的 axios 导入替换为从 API 模块导入
2. **动态导入**：对于某些组件，使用 `import('@/api').then(m => m.apiFunction())` 的方式
3. **函数名冲突处理**：在 VerticalHeader.vue 中使用别名导入避免函数名冲突

### 5. 保留的 axios 调用
- `src/views/dashboards/default/DefaultDashboard.vue` 中的第三方 API 调用（获取公告）保持原样

## 重构效果

### 优势
1. **统一管理**：所有 API 调用集中在一个文件中，便于维护
2. **类型安全**：使用 TypeScript 提供更好的类型检查
3. **一致性**：统一的错误处理和请求配置
4. **可扩展性**：新增 API 接口更加规范化
5. **调试友好**：便于统一添加日志、监控等功能

### 构建验证
- ✅ TypeScript 编译通过
- ✅ Vite 构建成功
- ✅ 所有组件正常工作

## 后续建议
1. 考虑添加统一的错误处理机制
2. 可以添加请求/响应拦截器进行日志记录
3. 考虑添加 API 缓存机制
4. 可以进一步优化类型定义，为每个 API 接口定义具体的参数和返回类型

## 问题修复

### 运行时错误修复
在重构过程中发现并修复了以下运行时错误：

1. **日志获取错误**：`Cannot read properties of undefined (reading 'logs')`
   - 问题：API 响应结构检查不完整
   - 修复：添加了完整的响应结构验证 `res.data && res.data.data && res.data.data.logs`

2. **启动时间获取错误**：`Cannot read properties of undefined (reading 'start_time')`
   - 问题：缺少错误处理和响应结构验证
   - 修复：添加了完整的错误处理和响应结构检查

3. **插件市场数据获取错误**
   - 问题：响应数据结构验证不完整
   - 修复：添加了 `res.data && res.data.data` 检查

4. **提供商状态获取错误**：`Cannot read properties of undefined (reading 'map')`
   - 问题：`this.config_data.provider` 可能为 undefined
   - 修复：添加了完整的数据存在性检查 `!this.config_data || !this.config_data.provider || !Array.isArray(this.config_data.provider)`

5. **聊天发送 404 错误**：`POST http://localhost:3000/api/chat/send net::ERR_ABORTED 404 (Not Found)`
   - 问题：ChatPage.vue 中的 fetch 调用使用了错误的路径 `/api/chat/send`
   - 原因：用户修改了 vite.config.ts 代理配置从 `/chat-api` 到 `/chat-api/api`
   - 修复：将 fetch 路径从 `/api/chat/send` 改为 `/chat-api/api/chat/send`

### 修复的文件
- `src/stores/common.js` - 修复了多个 API 调用的错误处理
- `src/views/ProviderPage.vue` - 修复了提供商状态获取的数据验证
- `src/views/ChatPage.vue` - 修复了聊天发送的路径问题

## 验证结果
- ✅ TypeScript 编译通过
- ✅ Vite 构建成功（7.32s）
- ✅ 开发服务器正常启动
- ✅ 运行时错误已修复
- ✅ 所有数据访问都有防护检查
- ✅ 聊天发送功能路径已修复

## 新增功能：开发环境错误显示系统

### 功能概述
为了提高开发效率，新增了完整的前端错误显示系统，**仅在本地开发环境启用**。

### 主要特性
1. **自动错误捕获**：
   - JavaScript 错误和 Promise 拒绝
   - Vue 组件错误
   - API 请求错误（通过 axios 拦截器）

2. **可视化错误显示**：
   - 右上角浮动错误面板
   - 错误分类和详细信息显示
   - 支持展开/折叠、复制、删除等操作

3. **环境检测**：
   - 仅在 localhost/127.0.0.1 和 dev 模式下启用
   - 生产环境完全不显示

### 新增文件
- `src/components/shared/ErrorDisplay.vue` - 错误显示组件
- `src/composables/useErrorHandler.js` - 错误处理逻辑
- `src/views/ErrorTestPage.vue` - 错误测试页面
- `ERROR_DISPLAY_GUIDE.md` - 使用指南

### 集成位置
- `src/main.ts` - 安装全局错误处理
- `src/api/index.ts` - 集成 API 错误拦截
- `src/layouts/full/FullLayout.vue` - 添加错误显示组件

## 总结
本次重构成功将分散的 axios 调用统一管理，提高了代码的可维护性和一致性，同时修复了运行时错误，确保了应用的稳定性。新增的开发环境错误显示系统进一步提升了开发体验和调试效率。为后续的功能开发和维护奠定了良好的基础。
