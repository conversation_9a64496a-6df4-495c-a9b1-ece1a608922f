const AuthRoutes = {
  path: '/auth',
  component: () => import('@/layouts/blank/BlankLayout.vue'),
  meta: {
    requiresAuth: false
  },
  children: [
    {
      name: 'SSOLogin',
      path: '/auth/sso-login',
      component: () => import('@/views/authentication/auth/SSOLoginPage.vue')
    },
    {
      name: 'OAuthCallback',
      path: '/callback',
      component: () => import('@/views/authentication/auth/OAuthCallback.vue')
    }
  ]
};

export default AuthRoutes;
