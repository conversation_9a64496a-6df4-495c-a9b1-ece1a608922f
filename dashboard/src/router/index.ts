import { createRouter, createWebHistory } from 'vue-router';
import MainRoutes from './MainRoutes';
import AuthRoutes from './AuthRoutes';
import ChatBoxRoutes from './ChatBoxRoutes';
import { useAuthStore } from '@/stores/auth';

export const router = createRouter({
  history: createWebHistory('/chat'),
  routes: [
    MainRoutes,
    AuthRoutes,
    ChatBoxRoutes
  ]
});

interface AuthStore {
  username: string;
  returnUrl: string | null;
  userInfo: any;
  isSSO: boolean;
  ssoLogin(token: string, userInfo: any): Promise<void>;
  checkSSOAuth(): Promise<boolean>;
  logout(): void;
  has_token(): boolean;
  isAuthenticated(): boolean;
}

router.beforeEach(async (to, _from, next) => {
  const publicPages = ['/auth/sso-login', '/callback'];
  const authRequired = !publicPages.includes(to.path);
  const auth: AuthStore = useAuthStore();


  // 如果用户已登录且试图访问 SSO 登录页面，则重定向到首页
  if (to.path === '/auth/sso-login' && auth.has_token()) {
    // 验证 SSO 令牌有效性
    const isValid = await auth.checkSSOAuth();
    if (isValid) {
      return next(auth.returnUrl || '/dashboard/default');
    }
  }

  // OAuth 回调页面不需要认证
  if (to.path === '/callback') {
    return next();
  }

  if (to.matched.some((record) => record.meta.requiresAuth)) {
    if (authRequired && !auth.has_token()) {
      auth.returnUrl = to.fullPath;
      return next('/auth/sso-login');
    } else {
      // 对于需要认证的页面，验证 SSO 令牌有效性
      const isValid = await auth.checkSSOAuth();
      if (!isValid) {
        auth.returnUrl = to.fullPath;
        return next('/auth/sso-login');
      }
      next();
    }
  } else {
    next();
  }
});
