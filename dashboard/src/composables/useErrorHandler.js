import { ref } from 'vue'

// 全局错误状态
const errors = ref([])
const isDevelopment = ref(
  import.meta.env.DEV || 
  import.meta.env.MODE === 'development' || 
  window.location.hostname === 'localhost' ||
  window.location.hostname === '127.0.0.1'
)

// 错误处理函数
export function useErrorHandler() {
  
  // 添加错误
  const addError = (error, context = {}) => {
    if (!isDevelopment.value) return
    
    const errorInfo = {
      id: Date.now() + Math.random(),
      type: error.type || context.type || 'Error',
      message: error.message || error.toString(),
      stack: error.stack,
      timestamp: new Date(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      component: context.component || null,
      action: context.action || null,
      data: context.data || null
    }
    
    errors.value.unshift(errorInfo)
    
    // 限制错误数量
    if (errors.value.length > 100) {
      errors.value = errors.value.slice(0, 100)
    }
    
    // 控制台输出（开发环境）
    console.error('Frontend Error:', errorInfo)
    
    return errorInfo.id
  }
  
  // API 错误处理
  const handleApiError = (error, context = {}) => {
    if (!isDevelopment.value) return
    
    let errorMessage = 'API 请求失败'
    let errorType = 'API Error'
    
    if (error.response) {
      // 服务器响应错误
      errorMessage = `${error.response.status} ${error.response.statusText}`
      if (error.response.data?.message) {
        errorMessage += `: ${error.response.data.message}`
      }
      errorType = `HTTP ${error.response.status}`
    } else if (error.request) {
      // 网络错误
      errorMessage = '网络请求失败，请检查网络连接'
      errorType = 'Network Error'
    } else {
      // 其他错误
      errorMessage = error.message || '未知错误'
    }
    
    return addError({
      type: errorType,
      message: errorMessage,
      stack: error.stack
    }, {
      ...context,
      url: error.config?.url,
      method: error.config?.method,
      data: error.config?.data
    })
  }
  
  // Vue 组件错误处理
  const handleVueError = (error, instance, info) => {
    if (!isDevelopment.value) return
    
    return addError(error, {
      type: 'Vue Error',
      component: instance?.$options.name || instance?.$options.__file || 'Unknown Component',
      info: info
    })
  }
  
  // 异步操作错误处理
  const handleAsyncError = async (asyncFn, context = {}) => {
    try {
      return await asyncFn()
    } catch (error) {
      addError(error, {
        type: 'Async Error',
        ...context
      })
      throw error // 重新抛出错误，让调用者决定如何处理
    }
  }
  
  // 包装函数，自动捕获错误
  const wrapWithErrorHandler = (fn, context = {}) => {
    return async (...args) => {
      try {
        return await fn(...args)
      } catch (error) {
        addError(error, context)
        throw error
      }
    }
  }
  
  // 清除错误
  const clearErrors = () => {
    errors.value = []
  }
  
  // 删除特定错误
  const removeError = (errorId) => {
    const index = errors.value.findIndex(error => error.id === errorId)
    if (index > -1) {
      errors.value.splice(index, 1)
    }
  }
  
  return {
    errors,
    isDevelopment,
    addError,
    handleApiError,
    handleVueError,
    handleAsyncError,
    wrapWithErrorHandler,
    clearErrors,
    removeError
  }
}

// 全局错误处理器实例
export const globalErrorHandler = useErrorHandler()

// 安装全局错误处理
export function installGlobalErrorHandler(app) {
  if (!globalErrorHandler.isDevelopment.value) return
  
  // Vue 全局错误处理
  app.config.errorHandler = (error, instance, info) => {
    globalErrorHandler.handleVueError(error, instance, info)
  }
  
  // 全局未捕获错误
  window.addEventListener('error', (event) => {
    globalErrorHandler.addError({
      type: 'JavaScript Error',
      message: event.error?.message || event.message,
      stack: event.error?.stack,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno
    })
  })
  
  // 全局未处理的 Promise 拒绝
  window.addEventListener('unhandledrejection', (event) => {
    globalErrorHandler.addError({
      type: 'Unhandled Promise Rejection',
      message: event.reason?.message || event.reason?.toString() || 'Unknown promise rejection',
      stack: event.reason?.stack
    })
  })
  
  // 暴露到全局，方便调试
  if (window) {
    window.__errorHandler = globalErrorHandler
  }
}
