import { ref } from 'vue'
import { generateAuthUrl } from '@/api'
import { globalErrorHandler } from '@/composables/useErrorHandler.js'

export function useSSOLogin() {
  const loading = ref(false)

  // 检查登录状态并清理旧令牌
  const checkLoginStatus = () => {
    // 清理可能存在的旧认证数据
    const urlParams = new URLSearchParams(window.location.search)
    const forceLogin = urlParams.get('force_login')

    if (forceLogin) {
      console.log('检测到强制登录参数，已清理所有认证数据')
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      localStorage.removeItem('oauth_state')
    }
  }

  // 处理 OAuth2 登录
  const handleOAuthLogin = async () => {
    loading.value = true
    try {
      // 生成随机 state 参数用于防止 CSRF 攻击
      const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
      localStorage.setItem('oauth_state', state)

      // 从后端获取配置并生成授权 URL
      const authUrl = await generateAuthUrl(state)
      console.log('跳转到授权 URL:', authUrl)
      
      // 跳转到授权服务器
      window.location.href = authUrl
    } catch (error: any) {
      console.error('OAuth 登录失败:', error)
      
      // 使用错误处理系统记录错误
      globalErrorHandler.addError(error, {
        type: 'OAuth Login Error',
        action: 'Generate Auth URL',
        component: 'SSOLogin'
      })
      
      loading.value = false
    }
  }

  return {
    loading,
    handleOAuthLogin,
    checkLoginStatus
  }
}
