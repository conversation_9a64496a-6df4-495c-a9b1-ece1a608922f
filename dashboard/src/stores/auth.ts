import { defineStore } from 'pinia';
import { router } from '@/router';
import { getCurrentUserInfo } from '@/api';

export const useAuthStore = defineStore({
  id: 'auth',
  state: () => ({
    // @ts-ignore
    username: '',
    returnUrl: null,
    userInfo: null as any,
    isSSO: false
  }),
  actions: {
    async ssoLogin(token: string, userInfo: any): Promise<void> {
      try {
        this.username = userInfo.username || userInfo.name || userInfo.id;
        this.userInfo = userInfo;
        this.isSSO = true;

        localStorage.setItem('user', this.username);
        localStorage.setItem('token', token);
        localStorage.setItem('sso_user_info', JSON.stringify(userInfo));
        localStorage.setItem('is_sso', 'true');

        router.push('/dashboard/default');
      } catch (error) {
        return Promise.reject(error);
      }
    },
    async checkSSOAuth(): Promise<boolean> {
      try {
        if (!this.has_token()) {
          return false;
        }

        // 如果是 SSO 登录，验证令牌有效性
        if (localStorage.getItem('token')) {
          const response = await getCurrentUserInfo();
          if (response.data) {
            this.userInfo = response.data;
            this.username = response.data.username || response.data.name || response.data.id;
            this.isSSO = true;
            return true;
          }
        }
        return false;
      } catch (error) {
        console.error('SSO 认证检查失败:', error);
        this.logout();
        return false;
      }
    },
    logout() {
      this.username = '';
      this.userInfo = null;
      this.isSSO = false;

      localStorage.removeItem('user');
      localStorage.removeItem('token');
      localStorage.removeItem('sso_user_info');
      localStorage.removeItem('is_sso');
      localStorage.removeItem('oauth_state');

      // 只支持 SSO 登录
      router.push('/auth/sso-login');
    },
    has_token(): boolean {
      return !!localStorage.getItem('token');
    },
    isAuthenticated(): boolean {
      return this.has_token() && !!this.username;
    }
  }
});
