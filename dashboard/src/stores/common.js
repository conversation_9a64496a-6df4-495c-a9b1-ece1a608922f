import { defineStore } from 'pinia';

import { getStartTime, getPluginMarketList } from '@/api';

export const useCommonStore = defineStore({
  id: 'common',
  state: () => ({
    startTime: -1,
    pluginMarketData: [],
  }),
  actions: {
    getStartTime() {
      if (this.startTime !== -1) {
        return this.startTime
      }
      getStartTime().then((res) => {
        if (res.data && res.data.data && res.data.data.start_time !== undefined) {
          this.startTime = res.data.data.start_time
        }
      }).catch((err) => {
        console.error('Failed to get start time:', err);
      })
    },
    async getPluginCollections(force = false) {
      // 获取插件市场数据
      if (!force && this.pluginMarketData.length > 0) {
        return Promise.resolve(this.pluginMarketData);
      }
      
      // 如果是强制刷新，添加 force_refresh 参数
      const url = force ? '/api/plugin/market_list?force_refresh=true' : '/api/plugin/market_list';
      
      return getPluginMarketList(force)
        .then((res) => {
          let data = []
          if (res.data && res.data.data) {
            for (let key in res.data.data) {
            data.push({
              "name": key,
              "desc": res.data.data[key].desc,
              "author": res.data.data[key].author,
              "repo": res.data.data[key].repo,
              "installed": false,
              "version": res.data.data[key]?.version ? res.data.data[key].version : "未知",
              "social_link": res.data.data[key]?.social_link,
              "tags": res.data.data[key]?.tags ? res.data.data[key].tags : [],
              "logo": res.data.data[key]?.logo ? res.data.data[key].logo : "",
              "pinned": res.data.data[key]?.pinned ? res.data.data[key].pinned : false,
              "stars": res.data.data[key]?.stars ? res.data.data[key].stars : 0,
              "updated_at": res.data.data[key]?.updated_at ? res.data.data[key].updated_at : "",
            })
          }
          }
          this.pluginMarketData = data;
          return data;
        })
        .catch((err) => {
          return Promise.reject(err);
        });
    },
  }
});
