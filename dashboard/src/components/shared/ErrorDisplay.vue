<template>
  <div v-if="isDevelopment && errors.length > 0" class="error-overlay">
    <v-card class="error-card" elevation="8">
      <v-card-title class="error-header">
        <v-icon color="error" class="mr-2">mdi-alert-circle</v-icon>
        前端错误 ({{ errors.length }})
        <v-spacer></v-spacer>
        <v-btn icon size="small" @click="clearErrors">
          <v-icon>mdi-close</v-icon>
        </v-btn>
        <v-btn icon size="small" @click="toggleMinimize">
          <v-icon>{{ isMinimized ? 'mdi-window-maximize' : 'mdi-window-minimize' }}</v-icon>
        </v-btn>
      </v-card-title>
      
      <v-card-text v-if="!isMinimized" class="error-content">
        <v-expansion-panels v-model="expandedPanel" multiple>
          <v-expansion-panel
            v-for="(error, index) in errors"
            :key="index"
            :value="index"
          >
            <v-expansion-panel-title>
              <div class="error-summary">
                <v-chip size="small" color="error" class="mr-2">
                  {{ error.type }}
                </v-chip>
                <span class="error-message">{{ error.message }}</span>
                <v-spacer></v-spacer>
                <span class="error-time">{{ formatTime(error.timestamp) }}</span>
              </div>
            </v-expansion-panel-title>
            
            <v-expansion-panel-text>
              <div class="error-details">
                <div v-if="error.stack" class="mb-3">
                  <strong>Stack Trace:</strong>
                  <pre class="error-stack">{{ error.stack }}</pre>
                </div>
                
                <div v-if="error.component" class="mb-3">
                  <strong>Component:</strong> {{ error.component }}
                </div>
                
                <div v-if="error.url" class="mb-3">
                  <strong>URL:</strong> {{ error.url }}
                </div>
                
                <div v-if="error.userAgent" class="mb-3">
                  <strong>User Agent:</strong> {{ error.userAgent }}
                </div>
                
                <div class="error-actions">
                  <v-btn size="small" color="primary" @click="copyError(error)">
                    <v-icon left>mdi-content-copy</v-icon>
                    复制错误信息
                  </v-btn>
                  <v-btn size="small" color="error" @click="removeError(index)">
                    <v-icon left>mdi-delete</v-icon>
                    删除此错误
                  </v-btn>
                </div>
              </div>
            </v-expansion-panel-text>
          </v-expansion-panel>
        </v-expansion-panels>
      </v-card-text>
    </v-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const errors = ref([])
const isMinimized = ref(false)
const expandedPanel = ref([])

// 检查是否为开发环境
const isDevelopment = computed(() => {
  return import.meta.env.DEV || 
         import.meta.env.MODE === 'development' || 
         window.location.hostname === 'localhost' ||
         window.location.hostname === '127.0.0.1'
})

// 添加错误
const addError = (error) => {
  if (!isDevelopment.value) return
  
  const errorInfo = {
    type: error.type || 'JavaScript Error',
    message: error.message || error.toString(),
    stack: error.stack,
    timestamp: new Date(),
    url: window.location.href,
    userAgent: navigator.userAgent,
    component: error.component || null
  }
  
  errors.value.unshift(errorInfo)
  
  // 限制错误数量，避免内存泄漏
  if (errors.value.length > 50) {
    errors.value = errors.value.slice(0, 50)
  }
  
  // 自动展开最新的错误
  expandedPanel.value = [0]
}

// 清除所有错误
const clearErrors = () => {
  errors.value = []
  expandedPanel.value = []
}

// 删除单个错误
const removeError = (index) => {
  errors.value.splice(index, 1)
}

// 切换最小化状态
const toggleMinimize = () => {
  isMinimized.value = !isMinimized.value
}

// 格式化时间
const formatTime = (timestamp) => {
  return timestamp.toLocaleTimeString()
}

// 复制错误信息
const copyError = async (error) => {
  const errorText = `
错误类型: ${error.type}
错误信息: ${error.message}
时间: ${error.timestamp.toLocaleString()}
URL: ${error.url}
Stack Trace:
${error.stack || '无'}
  `.trim()
  
  try {
    await navigator.clipboard.writeText(errorText)
    console.log('错误信息已复制到剪贴板')
  } catch (err) {
    console.error('复制失败:', err)
  }
}

// 全局错误处理
const handleError = (event) => {
  addError({
    type: 'JavaScript Error',
    message: event.error?.message || event.message,
    stack: event.error?.stack,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  })
}

// Promise 拒绝处理
const handleUnhandledRejection = (event) => {
  addError({
    type: 'Unhandled Promise Rejection',
    message: event.reason?.message || event.reason?.toString() || 'Unknown promise rejection',
    stack: event.reason?.stack
  })
}

// Vue 错误处理
const handleVueError = (error, instance, info) => {
  addError({
    type: 'Vue Error',
    message: error.message,
    stack: error.stack,
    component: instance?.$options.name || instance?.$options.__file || 'Unknown Component',
    info: info
  })
}

onMounted(() => {
  if (isDevelopment.value) {
    // 监听全局错误
    window.addEventListener('error', handleError)
    window.addEventListener('unhandledrejection', handleUnhandledRejection)
    
    // 暴露给全局使用
    window.__addError = addError
  }
})

onUnmounted(() => {
  if (isDevelopment.value) {
    window.removeEventListener('error', handleError)
    window.removeEventListener('unhandledrejection', handleUnhandledRejection)
    delete window.__addError
  }
})

// 暴露方法给父组件
defineExpose({
  addError,
  clearErrors
})
</script>

<style scoped>
.error-overlay {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  max-width: 600px;
  max-height: 80vh;
}

.error-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.error-header {
  background: #f44336;
  color: white;
  padding: 12px 16px;
}

.error-content {
  max-height: 60vh;
  overflow-y: auto;
}

.error-summary {
  display: flex;
  align-items: center;
  width: 100%;
}

.error-message {
  flex: 1;
  margin-right: 16px;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.error-time {
  font-size: 0.875rem;
  color: #666;
}

.error-details {
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
}

.error-stack {
  background: #f5f5f5;
  padding: 12px;
  border-radius: 4px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
  max-height: 200px;
  overflow-y: auto;
}

.error-actions {
  margin-top: 16px;
  display: flex;
  gap: 8px;
}
</style>
