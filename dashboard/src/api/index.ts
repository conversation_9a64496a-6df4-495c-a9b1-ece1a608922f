import axios from 'axios';
import { globalErrorHandler } from '@/composables/useErrorHandler.js';

// 统一设置后端 API 前缀
// 开发和生产环境都使用 '/chat-api/api'
const API_PREFIX = '/chat-api/api';

// 不需要授权的公开接口列表
const PUBLIC_ENDPOINTS = [
  '/oauth/config',
  '/oauth/token'
];

// 检查是否为公开接口
const isPublicEndpoint = (url: string): boolean => {
  return PUBLIC_ENDPOINTS.some(endpoint => url.includes(endpoint));
};

// 请求拦截器
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    // 只为非公开接口添加 Authorization 头
    if (token && !isPublicEndpoint(config.url || '')) {
      config.headers = config.headers || {};
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    globalErrorHandler.handleApiError(error, {
      type: 'Request Error',
      action: 'API Request Setup'
    });
    return Promise.reject(error);
  }
);

// 响应拦截器
axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    globalErrorHandler.handleApiError(error, {
      action: 'API Response',
      url: error.config?.url,
      method: error.config?.method?.toUpperCase()
    });
    return Promise.reject(error);
  }
);

// Dashboard 统计
export const getStat = (offsetSec?: number) => {
  const qs = offsetSec ? `?offset_sec=${offsetSec}` : '';
  return axios.get(`${API_PREFIX}/stat/get${qs}`);
};

export const getVersion = () => axios.get(`${API_PREFIX}/stat/version`);
// 已移除：版本更新相关接口

// 配置相关
export const getConfig = (pluginName?: string) => axios.get(`${API_PREFIX}/config/get`, { params: pluginName ? { plugin_name: pluginName } : undefined });
export const updateAstrbotConfig = (data: any) => axios.post(`${API_PREFIX}/config/astrbot/update`, data);



// Auth - 已移除传统登录，只支持 SSO

// 插件
export const getPlugins = () => axios.get(`${API_PREFIX}/plugin/get`);
export const updatePlugin = (name: string, proxy: string) => axios.post(`${API_PREFIX}/plugin/update`, { name, proxy });
export const installPlugin = (payload: any) => axios.post(`${API_PREFIX}/plugin/install`, payload);
export const installPluginUpload = (formData: FormData) => axios.post(`${API_PREFIX}/plugin/install-upload`, formData, { headers: { 'Content-Type': 'multipart/form-data' } });

// 会话/对话
export const listConversations = (params: any) => axios.get(`${API_PREFIX}/conversation/list`, { params });

// 插件市场
export const getOnlinePlugins = (params?: any) => axios.get(`${API_PREFIX}/plugin/get-online`, { params });
export const checkPlugin = (name: string) => axios.get(`${API_PREFIX}/plugin/get`, { params: { name } });

// 知识库相关（插件 API）
export const getKBCollections = () => axios.get(`${API_PREFIX}/plug/alkaid/kb/collections`);
export const createKBCollection = (data: any) => axios.post(`${API_PREFIX}/plug/alkaid/kb/create_collection`, data);

// 平台配置
export const getPlatformConfig = () => axios.get(`${API_PREFIX}/config/get`);

// 消息统计时间序列
export const getMessageSeries = (offsetSec: number) => axios.get(`${API_PREFIX}/stat/get?offset_sec=${offsetSec}`);

// 更新相关
// 已移除：仪表盘更新接口

// 平台管理
export const addPlatform = (data: any) => axios.post(`${API_PREFIX}/config/platform/new`, data);
export const updatePlatform = (data: any) => axios.post(`${API_PREFIX}/config/platform/update`, data);
export const deletePlatform = (id: string) => axios.post(`${API_PREFIX}/config/platform/delete`, { id });

// 服务提供商管理
export const addProvider = (data: any) => axios.post(`${API_PREFIX}/config/provider/new`, data);
export const updateProvider = (data: any) => axios.post(`${API_PREFIX}/config/provider/update`, data);
export const deleteProvider = (id: string) => axios.post(`${API_PREFIX}/config/provider/delete`, { id });
export const checkProviderStatus = (id: string) => axios.get(`${API_PREFIX}/config/provider/check_one?id=${id}`);

// 插件配置
export const updatePluginConfig = (pluginName: string, data: any) => axios.post(`${API_PREFIX}/config/plugin/update?plugin_name=${pluginName}`, data);

// MCP 工具
export const addMcpServer = (data: any) => axios.post(`${API_PREFIX}/tools/mcp/add`, data);
export const updateMcpServer = (data: any) => axios.post(`${API_PREFIX}/tools/mcp/update`, data);
export const getMcpServers = () => axios.get(`${API_PREFIX}/tools/mcp/servers`);
export const getMcpMarket = (params?: any) => axios.get(`${API_PREFIX}/tools/mcp/market`, { params });

// 长期记忆相关（插件 API）
export const addLtmMemory = (data: any) => axios.post(`${API_PREFIX}/plug/alkaid/ltm/graph/add`, data);
export const getLtmUserIds = () => axios.get(`${API_PREFIX}/plug/alkaid/ltm/user_ids`);

// 插件控制
export const enablePlugin = (name: string) => axios.post(`${API_PREFIX}/plugin/on`, { name });
export const disablePlugin = (name: string) => axios.post(`${API_PREFIX}/plugin/off`, { name });

// 会话管理
export const getSessionList = () => axios.get(`${API_PREFIX}/session/list`);

// 平台删除和更新
export const deletePlatformById = (id: string) => axios.post(`${API_PREFIX}/config/platform/delete`, { id });
export const updatePlatformById = (id: string, config: any) => axios.post(`${API_PREFIX}/config/platform/update`, { id, config });

// 统计相关
export const getStartTime = () => axios.get(`${API_PREFIX}/stat/start-time`);

// 知识库删除
export const deleteKBCollection = (collectionName: string) => axios.get(`${API_PREFIX}/plug/alkaid/kb/collection/delete`, { params: { collection_name: collectionName } });

// 控制台相关
// 已移除：控制台 pip 安装接口（归属更新模块）

// 插件管理扩展
export const uninstallPlugin = (name: string) => axios.post(`${API_PREFIX}/plugin/uninstall`, { name });
export const reloadPlugin = (name: string) => axios.post(`${API_PREFIX}/plugin/reload`, { name });
export const getPluginPlatformConfig = () => axios.get(`${API_PREFIX}/plugin/platform_enable/get`);
export const setPluginPlatformConfig = (data: any) => axios.post(`${API_PREFIX}/plugin/platform_enable/set`, data);

// 插件市场
export const getPluginMarketList = (forceRefresh = false) => {
  const url = forceRefresh ? `${API_PREFIX}/plugin/market_list?force_refresh=true` : `${API_PREFIX}/plugin/market_list`;
  return axios.get(url);
};

// 系统控制
export const restartCore = () => axios.post(`${API_PREFIX}/stat/restart-core`);

// 账户管理
export const editAccount = (data: any) => axios.post(`${API_PREFIX}/auth/account/edit`, data);

// 更新检查
// 已移除：更新检查接口

// MCP 测试
export const testMcpServer = (data: any) => axios.post(`${API_PREFIX}/tools/mcp/test`, data);

// 模型管理
export const getProviderModels = (providerId: string) => axios.get(`${API_PREFIX}/config/provider/model_list`, { params: { provider_id: providerId } });

// 长期记忆图谱
export const getLtmGraph = (params?: any) => axios.get(`${API_PREFIX}/plug/alkaid/ltm/graph`, { params });
export const searchLtmGraph = (params: any) => axios.get(`${API_PREFIX}/plug/alkaid/ltm/graph/search`, { params });
export const getLtmFact = (factId: string) => axios.get(`${API_PREFIX}/plug/alkaid/ltm/graph/fact`, { params: { fact_id: factId } });

// 知识库文件上传
export const uploadKBFile = (formData: FormData) => axios.post(`${API_PREFIX}/plug/alkaid/kb/collection/add_file`, formData, { headers: { 'Content-Type': 'multipart/form-data' } });

// 知识库搜索
export const searchKBCollection = (collectionName: string, query: string, topK: number) => axios.get(`${API_PREFIX}/plug/alkaid/kb/collection/search`, { params: { collection_name: collectionName, query, top_k: topK } });

// MCP 服务器管理
export const deleteMcpServer = (name: string) => axios.post(`${API_PREFIX}/tools/mcp/delete`, { name });

// 插件 README
export const getPluginReadme = (name: string) => axios.get(`${API_PREFIX}/plugin/readme`, { params: { name } });

// 代理测试
export const testGithubProxy = (proxyUrl: string) => axios.post(`${API_PREFIX}/stat/test-ghproxy-connection`, { proxy_url: proxyUrl });

// 对话管理
export const deleteConversation = (userId: string, cid: string) => axios.post(`${API_PREFIX}/conversation/delete`, { user_id: userId, cid });
export const getConversationDetail = (userId: string, cid: string) => axios.post(`${API_PREFIX}/conversation/detail`, { user_id: userId, cid });
export const updateConversationHistory = (userId: string, cid: string, history: any) => axios.post(`${API_PREFIX}/conversation/update_history`, { user_id: userId, cid, history });
export const updateConversation = (userId: string, cid: string, title: string) => axios.post(`${API_PREFIX}/conversation/update`, { user_id: userId, cid, title });

// 聊天相关
export const renameChatConversation = (conversationId: string, title: string) => axios.post(`${API_PREFIX}/chat/rename_conversation`, { conversation_id: conversationId, title });
export const getChatFile = (filename: string) => axios.get(`${API_PREFIX}/chat/get_file`, { params: { filename }, responseType: 'blob' });
export const getChatStatus = () => axios.get(`${API_PREFIX}/chat/status`);
export const uploadChatFile = (formData: FormData) => axios.post(`${API_PREFIX}/chat/post_file`, formData, { headers: { 'Content-Type': 'multipart/form-data' } });
export const uploadChatImage = (formData: FormData) => axios.post(`${API_PREFIX}/chat/post_image`, formData, { headers: { 'Content-Type': 'multipart/form-data' } });
export const getChatConversations = () => axios.get(`${API_PREFIX}/chat/conversations`);
export const getChatConversation = (conversationId: string) => axios.get(`${API_PREFIX}/chat/get_conversation`, { params: { conversation_id: conversationId } });
export const newChatConversation = () => axios.get(`${API_PREFIX}/chat/new_conversation`);
export const deleteChatConversation = (conversationId: string) => axios.get(`${API_PREFIX}/chat/delete_conversation`, { params: { conversation_id: conversationId } });
export const sendChatMessage = (data: any) => axios.post(`${API_PREFIX}/chat/send`, data);

// 会话隔离配置
export const getSessionSeparationStatus = () => axios.get(`${API_PREFIX}/config/provider/get_session_seperate`);
export const setSessionSeparationStatus = (enable: boolean) => axios.post(`${API_PREFIX}/config/provider/set_session_seperate`, { enable });

// 会话管理扩展
export const updateSessionPersona = (sessionId: string, personaName: string) => axios.post(`${API_PREFIX}/session/update_persona`, { session_id: sessionId, persona_name: personaName });
export const updateSessionProvider = (sessionId: string, providerId: string, providerType: string) => axios.post(`${API_PREFIX}/session/update_provider`, { session_id: sessionId, provider_id: providerId, provider_type: providerType });
export const updateSessionLLM = (sessionId: string, enabled: boolean) => axios.post(`${API_PREFIX}/session/update_llm`, { session_id: sessionId, enabled });
export const updateSessionTTS = (sessionId: string, enabled: boolean) => axios.post(`${API_PREFIX}/session/update_tts`, { session_id: sessionId, enabled });
export const getSessionPlugins = (sessionId: string) => axios.get(`${API_PREFIX}/session/plugins`, { params: { session_id: sessionId } });
export const updateSessionName = (sessionId: string, customName: string) => axios.post(`${API_PREFIX}/session/update_name`, { session_id: sessionId, custom_name: customName });

// LLM 工具配置
export const getLLMTools = () => axios.get(`${API_PREFIX}/config/llmtools`);

// 会话状态管理
export const updateSessionStatus = (sessionId: string, enabled: boolean) => axios.post(`${API_PREFIX}/session/update_status`, { session_id: sessionId, session_enabled: enabled });
export const updateSessionPlugin = (sessionId: string, pluginName: string, enabled: boolean) => axios.post(`${API_PREFIX}/session/update_plugin`, { session_id: sessionId, plugin_name: pluginName, enabled });

// 提供商配置列表
export const getProviderConfigList = (providerType?: string) => axios.get(`${API_PREFIX}/config/provider/list`, { params: providerType ? { provider_type: providerType } : undefined });

// SSO OAuth2 认证相关
// OAuth 配置接口 - 不需要授权
export const getOAuthConfig = () => {
  return axios.get(`${API_PREFIX}/oauth/config`, {
    headers: {
      // 明确不包含 Authorization 头
    }
  });
};
export const generateAuthUrl = (state: string) => {
  return getOAuthConfig().then(response => {
    const config = response.data;
    const params = new URLSearchParams({
      client_id: config.client_id,
      redirect_uri: config.redirect_uri,
      response_type: config.response_type,
      scope: config.scope,
      state: state
    });
    return `${config.authorize_url}?${params.toString()}`;
  });
};
// OAuth 令牌交换接口 - 不需要授权
export const exchangeToken = (code: string, state?: string) => {
  return axios.post(`${API_PREFIX}/oauth/token`, { code, state }, {
    headers: {
      // 明确不包含 Authorization 头
    }
  });
};
export const getCurrentUserInfo = () => axios.get(`${API_PREFIX}/oauth/me`);
export const getUserInfo = () => axios.get(`${API_PREFIX}/oauth/userinfo`);

