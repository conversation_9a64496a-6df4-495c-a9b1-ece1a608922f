<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 传统登录已移除，自动重定向到 SSO 登录
onMounted(() => {
  router.push('/auth/sso-login');
});
</script>

<template>
  <div class="d-flex align-center text-center mb-6">
    <div class="text-h4 w-100 text-primary">
      <router-link to="/" class="text-decoration-none text-primary">
        登录方式已更新
      </router-link>
    </div>
  </div>
  
  <v-card class="pa-6 text-center">
    <v-icon size="64" color="info" class="mb-4">mdi-information</v-icon>
    <h3 class="mb-4">登录方式已更新</h3>
    <p class="mb-4">传统的用户名密码登录已停用，请使用 SSO 单点登录。</p>
    <p class="text-caption mb-4">正在自动跳转到 SSO 登录页面...</p>
    <v-progress-linear indeterminate color="primary"></v-progress-linear>
  </v-card>
</template>

<style scoped>
.v-card {
  max-width: 400px;
  margin: 0 auto;
}
</style>
