<template>
  <div class="oauth-callback">
    <div class="callback-container">
      <div class="loading-content">
        <v-progress-circular
          indeterminate
          size="64"
          color="primary"
          class="mb-4"
        />
        <h3>正在处理登录...</h3>
        <p>请稍候，我们正在验证您的身份</p>
        
        <!-- 错误信息显示 -->
        <v-alert
          v-if="errorMessage"
          type="error"
          class="mt-4"
          :text="errorMessage"
        />
        
        <!-- 返回登录按钮 -->
        <v-btn
          v-if="errorMessage"
          color="primary"
          variant="outlined"
          class="mt-4"
          @click="goToLogin"
        >
          返回登录
        </v-btn>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { exchangeToken } from '@/api'
import { globalErrorHandler } from '@/composables/useErrorHandler.js'

const router = useRouter()
const route = useRoute()
const authStore = useAuthStore()
const errorMessage = ref('')

const goToLogin = () => {
  router.push('/auth/sso-login')
}

onMounted(async () => {
  try {
    // 获取 URL 中的授权码和状态
    const code = route.query.code as string
    const state = route.query.state as string
    const error = route.query.error as string

    console.log('OAuth 回调参数:', { code, state, error })

    // 检查是否有错误
    if (error) {
      console.error('OAuth 授权错误:', error)
      errorMessage.value = `授权失败：${error}`
      return
    }

    if (!code) {
      console.error('缺少授权码')
      errorMessage.value = '授权失败：缺少授权码'
      return
    }

    // 验证 state 参数（防止 CSRF 攻击）
    const savedState = localStorage.getItem('oauth_state')
    console.log('State 验证:', { receivedState: state, savedState })

    if (state && savedState && state !== savedState) {
      console.error('State 验证失败')
      errorMessage.value = '授权失败：状态验证失败'
      return
    }

    // 清除保存的 state
    localStorage.removeItem('oauth_state')

    console.log('开始交换访问令牌...')
    // 使用授权码换取访问令牌
    const response = await exchangeToken(code, state)
    const { access_token, user_info } = response.data

    console.log('访问令牌获取成功')
    
    // 保存令牌和用户信息
    localStorage.setItem('token', access_token)
    
    if (user_info) {
      console.log('保存用户信息:', user_info)
      localStorage.setItem('user', user_info.username || user_info.name || user_info.id)
      authStore.username = user_info.username || user_info.name || user_info.id
    }

    console.log(authStore)
    // 跳转到主页
    router.push('/dashboard/default')

  } catch (error: any) {
    alert('bbb')
    console.error('OAuth 回调处理失败:', error)
    
    // 使用错误处理系统记录错误
    globalErrorHandler.addError(error, {
      type: 'OAuth Callback Error',
      action: 'OAuth Token Exchange',
      component: 'OAuthCallback'
    })
    
    let errorMsg = '登录失败，请重试'
    if (error.response?.data?.error) {
      errorMsg = error.response.data.error
    } else if (error.message) {
      errorMsg = error.message
    }
    
    errorMessage.value = errorMsg
  }
})
</script>

<style scoped>
.oauth-callback {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem;
}

.callback-container {
  background: white;
  border-radius: 16px;
  padding: 3rem;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
  max-width: 400px;
  width: 100%;
  animation: container-appear 0.6s ease forwards;
}

.loading-content h3 {
  margin: 1.5rem 0 0.5rem 0;
  color: #333;
  font-weight: 600;
  font-size: 1.5rem;
}

.loading-content p {
  margin: 0;
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

@keyframes container-appear {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .oauth-callback {
    padding: 1rem;
  }
  
  .callback-container {
    padding: 2rem 1.5rem;
  }
  
  .loading-content h3 {
    font-size: 1.3rem;
  }
  
  .loading-content p {
    font-size: 0.9rem;
  }
}
</style>
