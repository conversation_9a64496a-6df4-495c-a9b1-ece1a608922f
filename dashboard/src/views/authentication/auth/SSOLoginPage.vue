<template>
  <div class="sso-login-page">
    <!-- 背景图层 -->
    <div class="login-bg-layer"></div>
    
    <div class="login-container">
      <!-- 左侧图片区域 -->
      <div class="login-image-section">
        <div class="login-image-content">
          <h1 class="brand-title">AstrBot 管理后台</h1>
          <p class="brand-slogan">探索人工智能的无限可能</p>
          <div class="brand-features">
            <div class="feature-item">
              <v-icon>mdi-robot</v-icon>
              <span>智能对话</span>
            </div>
            <div class="feature-item">
              <v-icon>mdi-cog</v-icon>
              <span>灵活配置</span>
            </div>
            <div class="feature-item">
              <v-icon>mdi-shield-check</v-icon>
              <span>安全可靠</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 右侧登录表单区域 -->
      <div class="login-form-section">
        <div class="login-form-wrapper">
          <div class="login-header">
            <div class="login-logo">
              <v-icon size="48" color="primary">mdi-robot</v-icon>
            </div>
            <h2 class="login-title">欢迎使用 AstrBot 管理后台</h2>
            <p class="login-subtitle">请使用 SSO 账号登录</p>
          </div>

          <div class="login-form">
            <div class="form-actions">
              <v-btn
                color="primary"
                size="large"
                block
                :loading="loading"
                @click="handleOAuthLogin"
                class="login-button"
                prepend-icon="mdi-account-key"
              >
                使用 SSO 登录
              </v-btn>

              <div class="login-info">
                <p>点击上方按钮将跳转到 SSO 登录页面</p>
                <p class="login-hint">登录后将自动返回到应用</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="login-footer">
      <span class="footer-text">Powered by AstrBot</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { useSSOLogin } from '@/composables/useSSOLogin'

const router = useRouter()
const authStore = useAuthStore()
const { loading, handleOAuthLogin } = useSSOLogin()

onMounted(() => {
  // 检查用户是否已登录
  if (authStore.has_token()) {
    router.push(authStore.returnUrl || '/dashboard/default')
  }
})
</script>

<style scoped>
.sso-login-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: 'Roboto', sans-serif;
}

/* 背景图层 */
.login-bg-layer {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -2;
}

.login-bg-layer::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.3) 0%, rgba(118, 75, 162, 0.3) 100%);
  z-index: -1;
}

.login-container {
  display: flex;
  width: 90%;
  max-width: 1200px;
  min-height: 600px;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: container-appear 0.8s ease forwards;
}

/* 左侧图片区域 */
.login-image-section {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.8) 0%, rgba(118, 75, 162, 0.8) 100%);
  color: white;
  overflow: hidden;
}

.login-image-content {
  position: relative;
  z-index: 1;
  width: 100%;
  text-align: center;
}

.brand-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 1rem;
  background: linear-gradient(to right, #ffffff, #e0e0ff);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: slide-in-left 0.8s ease forwards;
}

.brand-slogan {
  font-size: 1.5rem;
  margin-bottom: 3rem;
  opacity: 0;
  animation: fade-in 0.8s ease forwards 0.3s;
}

.brand-features {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  align-items: center;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 1.2rem;
  opacity: 0;
  transform: translateX(-20px);
  animation: slide-in-left 0.6s ease forwards;
}

.feature-item:nth-child(1) { animation-delay: 0.5s; }
.feature-item:nth-child(2) { animation-delay: 0.7s; }
.feature-item:nth-child(3) { animation-delay: 0.9s; }

/* 右侧表单区域 */
.login-form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: rgba(255, 255, 255, 0.95);
  position: relative;
}

.login-form-wrapper {
  width: 100%;
  max-width: 400px;
  position: relative;
  z-index: 1;
}

.login-header {
  text-align: center;
  margin-bottom: 2.5rem;
}

.login-logo {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.login-title {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
  animation: fade-in 0.8s ease forwards;
}

.login-subtitle {
  color: #666;
  font-size: 1rem;
  animation: fade-in 0.8s ease forwards 0.2s;
  opacity: 0;
}

.login-form {
  animation: slide-up 0.8s ease forwards 0.3s;
  opacity: 0;
}

.form-actions {
  margin-top: 2rem;
}

.login-button {
  height: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1rem;
}

.traditional-login-button {
  height: 50px;
  font-size: 1rem;
}

.login-info {
  text-align: center;
  margin: 1.5rem 0;
}

.login-info p {
  margin: 0.5rem 0;
  color: #666;
  font-size: 0.9rem;
}

.login-hint {
  color: #999 !important;
  font-size: 0.8rem !important;
}

.login-footer {
  text-align: center;
  margin-top: 2rem;
  position: absolute;
  bottom: 1rem;
  width: 100%;
  animation: fade-in 0.8s ease forwards 1s;
  opacity: 0;
}

.footer-text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
}

/* 动画 */
@keyframes container-appear {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slide-in-left {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式布局 */
@media (max-width: 992px) {
  .login-container {
    flex-direction: column;
    width: 95%;
    max-width: 500px;
  }
  
  .login-image-section {
    padding: 2rem;
    min-height: 250px;
  }
  
  .brand-title {
    font-size: 2.5rem;
  }
  
  .brand-slogan {
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
  }
  
  .brand-features {
    flex-direction: row;
    justify-content: space-around;
    gap: 1rem;
  }
  
  .feature-item {
    flex-direction: column;
    text-align: center;
    font-size: 1rem;
  }
}

@media (max-width: 768px) {
  .login-container {
    width: 100%;
    height: 100vh;
    border-radius: 0;
    margin: 0;
  }
  
  .login-image-section {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.5) 0%, rgba(118, 75, 162, 0.5) 100%);
  }
  
  .login-form-section {
    background-color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(15px);
    min-height: 100vh;
    padding: 2rem 1.5rem;
  }
  
  .brand-features {
    display: none;
  }
  
  .login-form-wrapper {
    padding: 2rem;
    border-radius: 20px;
    background-color: rgba(255, 255, 255, 0.95);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
}
</style>
