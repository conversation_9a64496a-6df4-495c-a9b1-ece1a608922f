<template>
  <v-container>
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>错误测试页面（仅开发环境）</v-card-title>
          <v-card-text>
            <p class="mb-4">此页面用于测试前端错误显示功能，仅在开发环境可见。</p>
            
            <v-row>
              <v-col cols="12" md="6">
                <v-card variant="outlined">
                  <v-card-title>JavaScript 错误</v-card-title>
                  <v-card-text>
                    <v-btn color="error" @click="throwJSError" class="mb-2" block>
                      抛出 JavaScript 错误
                    </v-btn>
                    <v-btn color="error" @click="throwTypeError" class="mb-2" block>
                      抛出 TypeError
                    </v-btn>
                    <v-btn color="error" @click="throwReferenceError" class="mb-2" block>
                      抛出 ReferenceError
                    </v-btn>
                  </v-card-text>
                </v-card>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-card variant="outlined">
                  <v-card-title>Promise 错误</v-card-title>
                  <v-card-text>
                    <v-btn color="warning" @click="throwPromiseRejection" class="mb-2" block>
                      未处理的 Promise 拒绝
                    </v-btn>
                    <v-btn color="warning" @click="throwAsyncError" class="mb-2" block>
                      异步函数错误
                    </v-btn>
                    <v-btn color="warning" @click="throwTimeoutError" class="mb-2" block>
                      延时错误
                    </v-btn>
                  </v-card-text>
                </v-card>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-card variant="outlined">
                  <v-card-title>API 错误</v-card-title>
                  <v-card-text>
                    <v-btn color="info" @click="triggerApiError" class="mb-2" block>
                      触发 API 错误
                    </v-btn>
                    <v-btn color="info" @click="triggerNetworkError" class="mb-2" block>
                      触发网络错误
                    </v-btn>
                    <v-btn color="info" @click="trigger404Error" class="mb-2" block>
                      触发 404 错误
                    </v-btn>
                  </v-card-text>
                </v-card>
              </v-col>
              
              <v-col cols="12" md="6">
                <v-card variant="outlined">
                  <v-card-title>Vue 组件错误</v-card-title>
                  <v-card-text>
                    <v-btn color="purple" @click="triggerVueError" class="mb-2" block>
                      触发 Vue 组件错误
                    </v-btn>
                    <v-btn color="purple" @click="triggerWatcherError" class="mb-2" block>
                      触发 Watcher 错误
                    </v-btn>
                    <v-btn color="purple" @click="triggerComputedError" class="mb-2" block>
                      触发 Computed 错误
                    </v-btn>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            
            <v-divider class="my-4"></v-divider>
            
            <v-row>
              <v-col cols="12">
                <v-card variant="outlined">
                  <v-card-title>错误控制</v-card-title>
                  <v-card-text>
                    <v-btn color="success" @click="clearAllErrors" class="mr-2">
                      清除所有错误
                    </v-btn>
                    <v-btn color="primary" @click="addCustomError">
                      添加自定义错误
                    </v-btn>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useErrorHandler } from '@/composables/useErrorHandler.js'
import axios from 'axios'

const { addError, clearErrors, handleAsyncError } = useErrorHandler()

// JavaScript 错误
const throwJSError = () => {
  throw new Error('这是一个测试的 JavaScript 错误')
}

const throwTypeError = () => {
  const obj = null
  obj.someProperty // 这会抛出 TypeError
}

const throwReferenceError = () => {
  console.log(undefinedVariable) // 这会抛出 ReferenceError
}

// Promise 错误
const throwPromiseRejection = () => {
  Promise.reject(new Error('这是一个未处理的 Promise 拒绝'))
}

const throwAsyncError = async () => {
  await handleAsyncError(async () => {
    throw new Error('这是一个异步函数错误')
  }, { action: 'Test Async Function' })
}

const throwTimeoutError = () => {
  setTimeout(() => {
    throw new Error('这是一个延时错误')
  }, 1000)
}

// API 错误
const triggerApiError = async () => {
  try {
    await axios.get('/chat-api/api/test/error')
  } catch (error) {
    // 错误会被 axios 拦截器自动处理
  }
}

const triggerNetworkError = async () => {
  try {
    await axios.get('http://nonexistent-domain.com/api/test')
  } catch (error) {
    // 错误会被 axios 拦截器自动处理
  }
}

const trigger404Error = async () => {
  try {
    await axios.get('/chat-api/api/nonexistent/endpoint')
  } catch (error) {
    // 错误会被 axios 拦截器自动处理
  }
}

// Vue 组件错误
const errorData = ref(null)

const triggerVueError = () => {
  // 故意访问 null 的属性来触发错误
  errorData.value = null
  console.log(errorData.value.nonExistentProperty)
}

const triggerWatcherError = () => {
  // 在 watcher 中抛出错误
  watch(() => errorData.value, () => {
    throw new Error('这是一个 Watcher 错误')
  }, { immediate: true })
  
  errorData.value = 'trigger watcher'
}

const triggerComputedError = () => {
  // 在 computed 中抛出错误
  const errorComputed = computed(() => {
    throw new Error('这是一个 Computed 错误')
  })
  
  // 访问 computed 值来触发错误
  console.log(errorComputed.value)
}

// 错误控制
const clearAllErrors = () => {
  clearErrors()
}

const addCustomError = () => {
  addError(new Error('这是一个自定义错误'), {
    type: 'Custom Error',
    action: 'Manual Test',
    component: 'ErrorTestPage'
  })
}
</script>

<style scoped>
.v-card {
  height: 100%;
}
</style>
