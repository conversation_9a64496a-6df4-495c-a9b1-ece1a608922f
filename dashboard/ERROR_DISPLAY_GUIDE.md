# 前端错误显示系统使用指南

## 概述

本系统为开发环境提供了完整的前端错误显示功能，帮助开发者快速发现和调试问题。**该功能仅在本地开发环境（localhost/127.0.0.1）和 dev 模式下启用，生产环境不会显示。**

## 功能特性

### 🎯 自动错误捕获
- **JavaScript 错误**：自动捕获所有未处理的 JavaScript 错误
- **Promise 拒绝**：捕获未处理的 Promise 拒绝
- **Vue 组件错误**：捕获 Vue 组件生命周期和渲染错误
- **API 错误**：自动捕获所有 axios 请求错误

### 🎨 可视化错误显示
- **浮动面板**：右上角显示错误面板，不影响正常使用
- **错误分类**：按错误类型分类显示（JavaScript、API、Vue 等）
- **详细信息**：显示错误堆栈、组件信息、时间戳等
- **可折叠**：支持展开/折叠查看详细信息

### 🛠️ 交互功能
- **复制错误**：一键复制错误信息到剪贴板
- **删除错误**：删除单个或所有错误
- **最小化**：最小化错误面板
- **自动展开**：新错误自动展开显示

## 环境检测

系统会自动检测以下条件来判断是否启用错误显示：

```javascript
const isDevelopment = 
  import.meta.env.DEV || 
  import.meta.env.MODE === 'development' || 
  window.location.hostname === 'localhost' ||
  window.location.hostname === '127.0.0.1'
```

## 使用方法

### 1. 自动错误捕获

系统已经自动配置，无需额外设置。以下错误会被自动捕获：

```javascript
// JavaScript 错误
throw new Error('这会被自动捕获')

// Promise 拒绝
Promise.reject(new Error('这也会被捕获'))

// API 错误（通过 axios 拦截器）
axios.get('/api/nonexistent') // 404 错误会被捕获
```

### 2. 手动添加错误

在需要的地方手动添加错误信息：

```javascript
import { useErrorHandler } from '@/composables/useErrorHandler.js'

const { addError } = useErrorHandler()

// 添加自定义错误
addError(new Error('自定义错误信息'), {
  type: 'Custom Error',
  action: 'User Action',
  component: 'MyComponent'
})
```

### 3. API 错误处理

API 模块已经集成了错误处理：

```javascript
// 在 src/api/index.ts 中已配置
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    globalErrorHandler.handleApiError(error, {
      action: 'API Response',
      url: error.config?.url,
      method: error.config?.method?.toUpperCase()
    })
    return Promise.reject(error)
  }
)
```

### 4. 异步错误处理

使用 `handleAsyncError` 包装异步操作：

```javascript
import { useErrorHandler } from '@/composables/useErrorHandler.js'

const { handleAsyncError } = useErrorHandler()

// 包装异步操作
const result = await handleAsyncError(async () => {
  // 可能出错的异步操作
  return await someAsyncOperation()
}, {
  action: 'Data Loading',
  component: 'MyComponent'
})
```

## 错误信息结构

每个错误包含以下信息：

```javascript
{
  id: 'unique-id',
  type: 'Error Type',           // 错误类型
  message: 'Error message',     // 错误信息
  stack: 'Stack trace',         // 堆栈信息
  timestamp: Date,              // 时间戳
  url: 'Current URL',           // 当前页面 URL
  userAgent: 'Browser info',    // 浏览器信息
  component: 'Component name',  // 组件名称（如果有）
  action: 'User action',        // 用户操作（如果有）
  data: {}                      // 额外数据（如果有）
}
```

## 测试错误显示

访问 `/error-test` 页面（仅开发环境）可以测试各种类型的错误：

- JavaScript 错误
- Promise 拒绝
- API 错误
- Vue 组件错误

## 文件结构

```
src/
├── components/shared/
│   └── ErrorDisplay.vue          # 错误显示组件
├── composables/
│   └── useErrorHandler.js        # 错误处理 composable
├── views/
│   └── ErrorTestPage.vue         # 错误测试页面（可选）
├── api/
│   └── index.ts                  # API 模块（已集成错误处理）
├── layouts/full/
│   └── FullLayout.vue            # 主布局（已集成错误显示）
└── main.ts                       # 主入口（已安装全局错误处理）
```

## 配置选项

### 错误数量限制

默认最多保存 100 个错误，可在 `useErrorHandler.js` 中修改：

```javascript
// 限制错误数量
if (errors.value.length > 100) {
  errors.value = errors.value.slice(0, 100)
}
```

### 环境检测自定义

可以在 `useErrorHandler.js` 中自定义环境检测逻辑：

```javascript
const isDevelopment = ref(
  // 自定义检测逻辑
  process.env.NODE_ENV === 'development' ||
  window.location.hostname.includes('dev')
)
```

## 最佳实践

1. **不要在生产环境启用**：确保错误显示仅在开发环境使用
2. **及时清理错误**：定期清理错误列表，避免内存占用过多
3. **添加上下文信息**：手动添加错误时提供足够的上下文信息
4. **测试错误场景**：使用错误测试页面验证错误处理是否正常

## 故障排除

### 错误不显示
1. 检查是否在开发环境
2. 检查浏览器控制台是否有错误
3. 确认 ErrorDisplay 组件已正确引入

### 错误信息不完整
1. 检查错误对象是否包含 stack 信息
2. 确认上下文信息是否正确传递

### 性能问题
1. 定期清理错误列表
2. 检查错误数量限制设置
3. 避免在错误处理中产生新的错误

## 总结

这个错误显示系统为开发环境提供了强大的错误调试能力，帮助开发者快速定位和解决问题。通过自动捕获和可视化显示，大大提高了开发效率和代码质量。
